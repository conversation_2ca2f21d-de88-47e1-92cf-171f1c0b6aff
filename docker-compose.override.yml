# Development overrides for docker-compose.yml
services:
  frontend:
    build:
      target: builder
    command: npm run dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8000
      - VITE_AI_SERVICE_URL=http://localhost:8001
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    environment:
      - DEBUG=true
      - RELOAD=true
    command: uvicorn server:app --host 0.0.0.0 --port 5000 --reload

  ai-service:
    environment:
      - DEBUG=true
      - RELOAD=true
    command: uvicorn main:app --host 0.0.0.0 --port 8002 --reload

  api-gateway:
    environment:
      - DEBUG=true
      - RELOAD=true
    command: uvicorn main:app --host 0.0.0.0 --port 8080 --reload
