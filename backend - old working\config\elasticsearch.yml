cluster.name: "elasticsearch"
node.name: "${<PERSON><PERSON><PERSON><PERSON><PERSON>}"

network.host: 0.0.0.0
http.port: 9200

# ---------------------------------- Discovery -----------------------------------
# Single-node discovery is crucial for a single-node setup
discovery.type: single-node
# Remove this line:
# cluster.initial_master_nodes: DESKTOP-GTLSNR4  # REMOVE THIS LINE
# ---------------------------------- Paths ------------------------------------
path.data: "./data"
path.logs: "./logs"

# ---------------------------------- Memory -----------------------------------
# Ensure enough memory is allocated to the JVM
bootstrap.memory_lock: true

# ---------------------------------- Heap Size -----------------------------------
# Set the JVM heap size.  Allocate about half of your RAM, but no more than 32GB.
# Updated for 32GB RAM:
-Xms16g
-Xmx16g

# ------------------------------ Garbage Collection ---------------------------
# Use G1GC for modern garbage collection
-XX:+UseG1GC
-XX:G1ReservePercent=15
-XX:InitiatingHeapOccupancyPercent=25
-XX:+DisableExplicitGC
-XX:+AlwaysPreTouch

# --------------------------------- Index -------------------------------------
# Single node optimization
index.number_of_shards: 1   # 1 primary shard is usually sufficient for a single node.
index.number_of_replicas: 0 # No replicas needed for a single node.  Important.
index.auto_expand_replicas: 0-0 # prevent auto expansion.

# -------------------------------- System ------------------------------------
# Disable node.roles that are not needed.  Helpful in some cases.
node.master: true # This node is the master
node.data: true   # This node holds data
node.ingest: true # This node can preprocess data

# --------------------------------- Cluster -----------------------------------
# Disable disk-based allocation throttling for a single node, as you likely have only one disk.
cluster.routing.allocation.disk.threshold_enabled: false
cluster.routing.allocation.enable: all
cluster.routing.allocation.node_concurrent_recoveries: 2 #can be lowered
cluster.routing.allocation.cluster_concurrent_rebalance: 0 # No rebalancing in single node

# ---------------------------------- Script -----------------------------------
script.allowed_types: inline,stored,file
script.context.painless.allow_unindexed_fields: true

# ---------------------------------- Security -----------------------------------
xpack.security.enabled: false # Disable security for development, set to true in production
# Ensure no conflicting security settings are present
#  Specifically remove any lines starting with  xpack.security.transport.ssl.*
# ----------------------------------  Warnings ---------------------------------
#Suppress warnings
deprecation.log_settings.level: off