from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
import redis.asyncio as redis
import elasticsearch
import httpx
import json
import uvicorn


ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# Configuration
mongo_url = os.environ.get('MONGO_URL', 'mongodb://localhost:27017')
db_name = os.environ.get('DB_NAME', 'superapp')
redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379')
elasticsearch_url = os.environ.get('ELASTICSEARCH_URL', 'http://localhost:9200')

# Database connections
client = AsyncIOMotorClient(mongo_url)
db = client[db_name]

# Redis connection
redis_client = None

# Elasticsearch connection
es_client = None

# Security
security = HTTPBearer(auto_error=False)

# Create the server app (separate from main app)
server_app = FastAPI(title="SuperApp Backend Server", version="1.0.0")

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Startup event
@server_app.on_event("startup")
async def startup_event():
    global redis_client, es_client
    
    # Connect to Redis
    try:
        redis_client = redis.from_url(redis_url, decode_responses=True)
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
    
    # Connect to Elasticsearch
    try:
        es_client = elasticsearch.AsyncElasticsearch([elasticsearch_url])
        if await es_client.ping():
            logger.info("Connected to Elasticsearch successfully")
        else:
            logger.error("Failed to connect to Elasticsearch")
    except Exception as e:
        logger.error(f"Elasticsearch connection error: {e}")

@server_app.on_event("shutdown")
async def shutdown_event():
    global redis_client, es_client
    if redis_client:
        await redis_client.close()
    if es_client:
        await es_client.close()
    client.close()


# Authentication middleware
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not credentials:
        return {"user_id": "anonymous", "authenticated": False}
    return {"user_id": "authenticated_user", "authenticated": True, "token": credentials.credentials}

# Define Models
class StatusCheck(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    client_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class StatusCheckCreate(BaseModel):
    client_name: str

class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    username: str
    email: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True
    preferences: Dict[str, Any] = Field(default_factory=dict)

class UserCreate(BaseModel):
    username: str
    email: str
    preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)

class Service(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    category: str
    endpoint: str
    status: str = "active"
    created_at: datetime = Field(default_factory=datetime.utcnow)
    usage_count: int = 0

class ServiceCreate(BaseModel):
    name: str
    description: str
    category: str
    endpoint: str

class SearchQuery(BaseModel):
    query: str
    category: Optional[str] = None
    limit: int = 10
    offset: int = 0

class AnalyticsQuery(BaseModel):
    timeframe: str = "day"  # day, week, month
    service_id: Optional[str] = None
    user_id: Optional[str] = None

# Add your routes to the router instead of directly to app
@api_router.get("/")
async def root():
    return {"message": "Hello World"}

@api_router.post("/status", response_model=StatusCheck)
async def create_status_check(input: StatusCheckCreate):
    status_dict = input.dict()
    status_obj = StatusCheck(**status_dict)
    _ = await db.status_checks.insert_one(status_obj.dict())
    return status_obj

@api_router.get("/status", response_model=List[StatusCheck])
async def get_status_checks():
    status_checks = await db.status_checks.find().to_list(1000)
    return [StatusCheck(**status_check) for status_check in status_checks]

# User Management Endpoints
@api_router.post("/users", response_model=User)
async def create_user(user_data: UserCreate, current_user=Depends(get_current_user)):
    """Create a new user"""
    user_dict = user_data.dict()
    user_obj = User(**user_dict)
    
    # Check if user already exists
    existing_user = await db.users.find_one({"email": user_obj.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="User with this email already exists")
    
    await db.users.insert_one(user_obj.dict())
    
    # Index user in Elasticsearch
    if es_client:
        try:
            await es_client.index(
                index="users",
                id=user_obj.id,
                body=user_obj.dict()
            )
        except Exception as e:
            logger.error(f"Failed to index user in Elasticsearch: {e}")
    
    return user_obj

@api_router.get("/users/{user_id}", response_model=User)
async def get_user(user_id: str, current_user=Depends(get_current_user)):
    """Get user by ID"""
    user = await db.users.find_one({"id": user_id})
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return User(**user)

@api_router.get("/users", response_model=List[User])
async def list_users(limit: int = 10, offset: int = 0, current_user=Depends(get_current_user)):
    """List users with pagination"""
    users = await db.users.find().skip(offset).limit(limit).to_list(limit)
    return [User(**user) for user in users]

@api_router.put("/users/{user_id}", response_model=User)
async def update_user(user_id: str, user_data: UserCreate, current_user=Depends(get_current_user)):
    """Update user"""
    existing_user = await db.users.find_one({"id": user_id})
    if not existing_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    update_data = user_data.dict(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow()
    
    await db.users.update_one({"id": user_id}, {"$set": update_data})
    
    updated_user = await db.users.find_one({"id": user_id})
    return User(**updated_user)

# Service Management Endpoints
@api_router.post("/services", response_model=Service)
async def create_service(service_data: ServiceCreate, current_user=Depends(get_current_user)):
    """Create a new service"""
    service_dict = service_data.dict()
    service_obj = Service(**service_dict)
    
    await db.services.insert_one(service_obj.dict())
    
    # Index service in Elasticsearch
    if es_client:
        try:
            await es_client.index(
                index="services",
                id=service_obj.id,
                body=service_obj.dict()
            )
        except Exception as e:
            logger.error(f"Failed to index service in Elasticsearch: {e}")
    
    return service_obj

@api_router.get("/services", response_model=List[Service])
async def list_services(category: Optional[str] = None, limit: int = 10, offset: int = 0):
    """List services with optional category filter"""
    query = {}
    if category:
        query["category"] = category
    
    services = await db.services.find(query).skip(offset).limit(limit).to_list(limit)
    return [Service(**service) for service in services]

@api_router.get("/services/{service_id}", response_model=Service)
async def get_service(service_id: str):
    """Get service by ID"""
    service = await db.services.find_one({"id": service_id})
    if not service:
        raise HTTPException(status_code=404, detail="Service not found")
    return Service(**service)

@api_router.put("/services/{service_id}/usage")
async def increment_service_usage(service_id: str, current_user=Depends(get_current_user)):
    """Increment service usage count"""
    result = await db.services.update_one(
        {"id": service_id},
        {"$inc": {"usage_count": 1}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Service not found")
    
    # Log usage for analytics
    usage_log = {
        "service_id": service_id,
        "user_id": current_user.get("user_id", "anonymous"),
        "timestamp": datetime.utcnow(),
        "action": "service_call"
    }
    await db.usage_logs.insert_one(usage_log)
    
    return {"message": "Usage incremented successfully"}

# Search Endpoints
@api_router.post("/search/services")
async def search_services(search_query: SearchQuery):
    """Search services using Elasticsearch or MongoDB fallback"""
    
    if es_client:
        try:
            # Use Elasticsearch for advanced search
            es_query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": search_query.query,
                                    "fields": ["name^2", "description", "category"]
                                }
                            }
                        ]
                    }
                },
                "from": search_query.offset,
                "size": search_query.limit
            }
            
            if search_query.category:
                es_query["query"]["bool"]["filter"] = [
                    {"term": {"category.keyword": search_query.category}}
                ]
            
            response = await es_client.search(
                index="services",
                body=es_query
            )
            
            services = []
            for hit in response["hits"]["hits"]:
                service_data = hit["_source"]
                service_data["score"] = hit["_score"]
                services.append(service_data)
            
            return {
                "services": services,
                "total": response["hits"]["total"]["value"],
                "took": response["took"]
            }
            
        except Exception as e:
            logger.error(f"Elasticsearch search failed: {e}")
    
    # Fallback to MongoDB text search
    query = {
        "$or": [
            {"name": {"$regex": search_query.query, "$options": "i"}},
            {"description": {"$regex": search_query.query, "$options": "i"}}
        ]
    }
    
    if search_query.category:
        query["category"] = search_query.category
    
    services = await db.services.find(query).skip(search_query.offset).limit(search_query.limit).to_list(search_query.limit)
    total = await db.services.count_documents(query)
    
    return {
        "services": [Service(**service).dict() for service in services],
        "total": total,
        "took": 0
    }

# Analytics Endpoints
@api_router.post("/analytics/usage")
async def get_usage_analytics(analytics_query: AnalyticsQuery, current_user=Depends(get_current_user)):
    """Get usage analytics"""
    
    # Calculate time range based on timeframe
    from datetime import timedelta
    now = datetime.utcnow()
    
    if analytics_query.timeframe == "day":
        start_time = now - timedelta(days=1)
    elif analytics_query.timeframe == "week":
        start_time = now - timedelta(weeks=1)
    elif analytics_query.timeframe == "month":
        start_time = now - timedelta(days=30)
    else:
        start_time = now - timedelta(days=1)
    
    # Build aggregation pipeline
    match_stage = {
        "timestamp": {"$gte": start_time, "$lte": now}
    }
    
    if analytics_query.service_id:
        match_stage["service_id"] = analytics_query.service_id
    
    if analytics_query.user_id:
        match_stage["user_id"] = analytics_query.user_id
    
    # Aggregate usage data
    pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": {
                    "service_id": "$service_id",
                    "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$timestamp"}}
                },
                "count": {"$sum": 1}
            }
        },
        {"$sort": {"_id.date": 1}}
    ]
    
    usage_data = await db.usage_logs.aggregate(pipeline).to_list(1000)
    
    # Get top services
    top_services_pipeline = [
        {"$match": match_stage},
        {
            "$group": {
                "_id": "$service_id",
                "count": {"$sum": 1}
            }
        },
        {"$sort": {"count": -1}},
        {"$limit": 10}
    ]
    
    top_services_data = await db.usage_logs.aggregate(top_services_pipeline).to_list(10)
    
    # Get service names for top services
    service_ids = [item["_id"] for item in top_services_data]
    services = await db.services.find({"id": {"$in": service_ids}}).to_list(10)
    service_names = {service["id"]: service["name"] for service in services}
    
    top_services = [
        {
            "service_id": item["_id"],
            "service_name": service_names.get(item["_id"], "Unknown"),
            "usage_count": item["count"]
        }
        for item in top_services_data
    ]
    
    return {
        "timeframe": analytics_query.timeframe,
        "start_time": start_time.isoformat(),
        "end_time": now.isoformat(),
        "usage_data": usage_data,
        "top_services": top_services,
        "total_requests": sum(item["count"] for item in usage_data)
    }

# Include the router in the server app
server_app.include_router(api_router)

server_app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Health check endpoint
@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "mongodb": "connected",
            "redis": "connected" if redis_client else "disconnected",
            "elasticsearch": "connected" if es_client else "disconnected"
        }
    }
    
    # Test MongoDB connection
    try:
        await db.command("ping")
    except Exception as e:
        health_status["services"]["mongodb"] = f"error: {str(e)}"
        health_status["status"] = "degraded"
    
    # Test Redis connection
    if redis_client:
        try:
            await redis_client.ping()
        except Exception as e:
            health_status["services"]["redis"] = f"error: {str(e)}"
            health_status["status"] = "degraded"
    
    # Test Elasticsearch connection
    if es_client:
        try:
            await es_client.ping()
        except Exception as e:
            health_status["services"]["elasticsearch"] = f"error: {str(e)}"
            health_status["status"] = "degraded"
    
    return health_status

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        workers=1
    )
