# SuperApp Docker Management
.PHONY: help build up down logs clean dev prod restart status

# Default target
help:
	@echo "SuperApp Docker Commands:"
	@echo "  make build     - Build all Docker images"
	@echo "  make up        - Start all services (production)"
	@echo "  make dev       - Start all services (development)"
	@echo "  make down      - Stop all services"
	@echo "  make logs      - View logs from all services"
	@echo "  make restart   - Restart all services"
	@echo "  make status    - Show status of all services"
	@echo "  make clean     - Remove all containers, images, and volumes"
	@echo ""
	@echo "Individual service commands:"
	@echo "  make logs-frontend    - View frontend logs"
	@echo "  make logs-backend     - View backend logs"
	@echo "  make logs-ai          - View AI service logs"
	@echo "  make logs-gateway     - View API gateway logs"

# Production commands
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

# Development commands
dev:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

dev-build:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml build

dev-down:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

# Utility commands
logs:
	docker-compose logs -f

logs-frontend:
	docker-compose logs -f frontend

logs-backend:
	docker-compose logs -f backend

logs-ai:
	docker-compose logs -f ai-service

logs-gateway:
	docker-compose logs -f api-gateway

restart:
	docker-compose restart

status:
	docker-compose ps

# Cleanup commands
clean:
	docker-compose down -v --rmi all --remove-orphans
	docker system prune -f

clean-volumes:
	docker-compose down -v

# Health check
health:
	@echo "Checking service health..."
	@curl -f http://localhost/health || echo "Frontend: DOWN"
	@curl -f http://localhost:8000/health || echo "Backend: DOWN"
	@curl -f http://localhost:8001/health || echo "AI Service: DOWN"
	@curl -f http://localhost:8002/health || echo "API Gateway: DOWN"
