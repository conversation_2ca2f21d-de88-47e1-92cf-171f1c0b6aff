import axios from 'axios';

const BACKEND_URL = '/api';
const AI_SERVICE_URL = '/ai';

// Create axios instances for both services
const backendApi = axios.create({
  baseURL: BACKEND_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

const aiApi = axios.create({
  baseURL: AI_SERVICE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Add response interceptor for error handling
const errorInterceptor = (error: any) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.error('Response error:', {
      data: error.response.data,
      status: error.response.status,
      headers: error.response.headers,
    });
    throw new Error(error.response.data?.detail || 'Server error');
  } else if (error.request) {
    // The request was made but no response was received
    console.error('Request error:', error.request);
    throw new Error('No response from server. Please check your connection.');
  } else {
    // Something happened in setting up the request that triggered an Error
    console.error('Error:', error.message);
    throw new Error('Failed to make request. Please try again.');
  }
};

backendApi.interceptors.response.use((response) => response, errorInterceptor);
aiApi.interceptors.response.use((response) => response, errorInterceptor);

// Types
export interface SearchParams {
  query: string;
  type?: 'all' | 'products' | 'services' | 'apps' | 'docs';
  page?: number;
  per_page?: number;
  lat?: number;
  lng?: number;
  radius_km?: number;
}

export interface Location {
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  distance?: {
    value: number;
    unit: string;
  };
}

export interface Rating {
  average: number;
  count: number;
}

export interface Item {
  id: string;
  name: string;
  description: string;
  images?: Array<{
    url: string;
    alt?: string;
    is_primary?: boolean;
    alt_text?: string;
  }>;
  category?: string[];
  tags?: string[];
  location?: Location;
  rating?: Rating;
  created_at?: string;
  updated_at?: string;
  price?: {
    amount: number;
    currency: string;
    discount_percentage?: number;
    original_amount?: number;
    tax_included?: boolean;
  };
  seller?: string;
  type?: 'product' | 'service' | 'store';
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: {
    source?: string;
    confidence?: number;
    tools_used?: string[];
  };
}

export interface ChatResponse {
  message: ChatMessage;
  sources?: {
    url: string;
    title: string;
    snippet: string;
  }[];
  actions?: {
    type: string;
    data: any;
  }[];
}

const api = {
  // Search functionality
  search: async ({ query, type = 'all', page = 1, per_page = 10, lat, lng, radius_km }: SearchParams) => {
    try {
      const response = await backendApi.get('/search/', {
        params: {
          query,
          type,
          page,
          per_page,
          lat,
          lng,
          radius_km
        },
      });
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // Get item details
  getItemDetails: async (id: string): Promise<Item> => {
    try {
      const response = await backendApi.get(`/items/${id}`);
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // AI chat functionality
  chat: async (messages: ChatMessage[], context?: any): Promise<ChatResponse> => {
    try {
      const response = await aiApi.post('/chat', { messages, context });
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // Product details
  getProduct: async (productId: string) => {
    try {
      const response = await backendApi.get(`/products/${productId}`);
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // Service details
  getService: async (serviceId: string) => {
    try {
      const response = await backendApi.get(`/services/${serviceId}`);
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // AI inference functionality
  inference: async (data: any) => {
    try {
      const response = await aiApi.post('/inference', data);
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },

  // Get document by ID (generic)
  getDocumentById: async (id: string, type: string) => {
    try {
      const endpoint = type === 'product' ? '/products' : '/services';
      const response = await backendApi.get(`${endpoint}/${id}`);
      return response.data;
    } catch (error) {
      return errorInterceptor(error);
    }
  },
};

export const { getItemDetails } = api;
export default api;