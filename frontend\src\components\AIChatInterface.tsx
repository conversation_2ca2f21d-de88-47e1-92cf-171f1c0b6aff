import React, { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, <PERSON>clip, Send, Settings } from 'lucide-react';
import AIInputBar from './AIInputBar';
import { aiChat, ChatMessage, ChatResponse } from '../services/aiChat';
import { Item } from '../services/api';

interface Message extends ChatMessage {
  id: string;
  timestamp: Date;
  sources?: {
    url: string;
    title: string;
    snippet: string;
  }[];
  actions?: {
    type: string;
    data: any;
  }[];
}

interface AIConfig {
  mode: 'chat' | 'completion' | 'agent';
  model: string;
  mcp: string;
  browsing: boolean;
  tools: boolean;
}

interface Props {
  item?: Item;
  onAction?: (action: { type: string; data: any }) => void;
}

const AIChatInterface: React.FC<Props> = ({ item, onAction }) => {
  const [messages, setMessages] = useState<Message[]>([{
    id: '0',
    role: 'system',
    content: 'You are a helpful AI assistant.',
    timestamp: new Date()
  }]);
  const [showConfig, setShowConfig] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  const [config, setConfig] = useState<AIConfig>({
    mode: 'chat',
    model: 'sarvam-m',
    mcp: 'default',
    browsing: true,
    tools: true
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async (text: string, files: File[], currentConfig: AIConfig) => {
    if (!text.trim() && files.length === 0) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: text.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setIsLoading(true);

    try {
      // Include the new message in the messages to send
      const messagesToSend = [...messages, newMessage].filter(msg => msg.role !== 'system').map(({ id, timestamp, ...msg }) => msg);
      console.log('Messages to send:', messagesToSend);

      const response = await aiChat.sendMessage(
        messagesToSend,
        {
          item,
          model: currentConfig.model,
          browsing: currentConfig.browsing,
          tools: currentConfig.tools ? ['search', 'browse', 'calculate', 'recommend'] : undefined
        }
      );

      console.log('AI Response received:', response);
      console.log('Message content:', response.message.content);

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.message.content,
        timestamp: new Date(),
        metadata: response.message.metadata,
        sources: response.sources,
        actions: response.actions
      };

      console.log('AI Response object:', aiResponse);
      setMessages(prev => {
        const newMessages = [...prev, aiResponse];
        console.log('Updated messages:', newMessages);
        return newMessages;
      });

      // Handle any actions returned by the AI
      if (response.actions && onAction) {
        response.actions.forEach(action => {
          onAction(action);
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingsClick = () => {
    setShowConfig(!showConfig);
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-indigo-600 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <h2 className="ml-3 text-lg font-semibold text-gray-900">AI Assistant</h2>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSettingsClick}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
          >
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-scroll p-4 space-y-4 min-h-0" style={{ maxHeight: 'calc(100vh - 200px)' }}>
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className="max-w-[80%] space-y-2">
              <div
                className={`rounded-lg px-4 py-2 ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-purple-400 to-indigo-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.content}
              </div>

              {/* Sources */}
              {message.sources && message.sources.length > 0 && (
                <div className="text-sm text-gray-500 pl-2">
                  <div className="font-medium mb-1">Sources:</div>
                  <ul className="space-y-1">
                    {message.sources.map((source, index) => (
                      <li key={index}>
                        <a
                          href={source.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-400 hover:text-purple-700"
                        >
                          {source.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Tools Used */}
              {message.metadata?.tools_used && message.metadata.tools_used.length > 0 && (
                <div className="text-xs text-gray-500 pl-2">
                  Tools used: {message.metadata.tools_used.join(', ')}
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 flex-shrink-0">
        <AIInputBar
          onSubmit={handleSend}
          config={config}
          onConfigChange={setConfig}
          onSettingsClick={handleSettingsClick}
          showConfigPanel={showConfig}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default AIChatInterface;
