services:
  frontend:
    build:
      context: ./frontend
      target: runner
    ports:
      - "${PORT:-80}:80"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - VITE_API_URL=${API_URL:-http://backend:8000}
      - VITE_AI_SERVICE_URL=${AI_SERVICE_URL:-http://ai-service:8001}
    depends_on:
      - backend
      - ai-service

  backend:
    build: ./backend
    ports:
      - "8000:5000"
    volumes:
      - ./backend:/app
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      - mongodb
      - elasticsearch

  ai-service:
    build: ./ai-service
    ports:
      - "8001:8002"
    volumes:
      - ./ai-service:/app
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SARVAM_API_KEY=${SARVAM_API_KEY}
      - BACKEND_URL=http://backend:8000
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis

  api-gateway:
    build: ./api-gateway
    ports:
      - "8002:8080"
    volumes:
      - ./api-gateway:/app
    depends_on:
      - backend
      - ai-service

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=superapp

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.10
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ulimits:
      memlock:
        soft: -1
        hard: -1

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
  elasticsearch_data:
  redis_data: