services:
  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8090:80"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=http://localhost:8100
      - VITE_AI_SERVICE_URL=http://localhost:8101
    depends_on:
      - backend
      - ai-service
    restart: unless-stopped

  # Backend FastAPI Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8100:8000"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017/superapp
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=production
    depends_on:
      - mongodb
      - elasticsearch
      - redis
    restart: unless-stopped

  # AI Service
  ai-service:
    build:
      context: ./ai-service
      dockerfile: Dockerfile
    ports:
      - "8101:8001"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - SARVAM_API_KEY=${SARVAM_API_KEY:-}
      - BACKEND_URL=http://backend:8000
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=production
    depends_on:
      - redis
      - backend
    restart: unless-stopped

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "8102:8002"
    environment:
      - BACKEND_URL=http://backend:8000
      - AI_SERVICE_URL=http://ai-service:8001
      - ENVIRONMENT=production
    depends_on:
      - backend
      - ai-service
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_DATABASE=superapp
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init:/docker-entrypoint-initdb.d
    restart: unless-stopped

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9201:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped

  # Elasticsearch Initialization
  elasticsearch-init:
    build: ./elasticsearch-init
    environment:
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DB=superapp
      - ELASTICSEARCH_INDEX=search
    depends_on:
      - elasticsearch
      - mongodb
    restart: "no"

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

# Named volumes for data persistence
volumes:
  mongodb_data:
  elasticsearch_data:
  redis_data:

# Network configuration
networks:
  default:
    name: superapp-network
