# SuperApp Docker Setup

This document explains how to run SuperApp using Docker and Docker Compose.

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Docker Compose v2.0+ 
- At least 4GB RAM available for containers

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
# At minimum, set your OpenAI and Sarvam API keys
```

### 2. Production Deployment
```bash
# Build and start all services
make up

# Or using docker-compose directly
docker-compose up -d
```

### 3. Development Mode
```bash
# Start in development mode (with hot reload)
make dev

# Or using docker-compose directly
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## 📋 Available Commands

### Using Makefile (Recommended)
```bash
make help          # Show all available commands
make build         # Build all images
make up            # Start production services
make dev           # Start development services
make down          # Stop all services
make logs          # View all logs
make status        # Check service status
make clean         # Clean up everything
```

### Using Docker Compose Directly
```bash
# Production
docker-compose up -d
docker-compose down
docker-compose logs -f

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
```

## 🌐 Service URLs

### Production Mode
- **Frontend**: http://localhost
- **Backend API**: http://localhost:8000
- **AI Service**: http://localhost:8001
- **API Gateway**: http://localhost:8002

### Development Mode
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **AI Service**: http://localhost:8001
- **API Gateway**: http://localhost:8002

### Database Services
- **MongoDB**: localhost:27017
- **Elasticsearch**: http://localhost:9200
- **Redis**: localhost:6379

## 🔧 Configuration

### Environment Variables
Key variables in `.env`:
```bash
# Required API Keys
OPENAI_API_KEY=your_openai_key
SARVAM_API_KEY=your_sarvam_key

# Database URLs (automatically configured for Docker)
MONGODB_URL=mongodb://mongodb:27017/superapp
ELASTICSEARCH_URL=http://elasticsearch:9200
REDIS_URL=redis://redis:6379
```

### Development vs Production
- **Production**: Optimized builds, nginx serving static files
- **Development**: Hot reload, source code mounted as volumes

## 📊 Monitoring

### Check Service Health
```bash
make health
# Or manually:
curl http://localhost/health
curl http://localhost:8000/health
curl http://localhost:8001/health
curl http://localhost:8002/health
```

### View Logs
```bash
# All services
make logs

# Individual services
make logs-frontend
make logs-backend
make logs-ai
make logs-gateway
```

### Check Status
```bash
make status
# Shows running containers and their status
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   netstat -tulpn | grep :80
   netstat -tulpn | grep :8000
   ```

2. **Out of disk space**
   ```bash
   # Clean up Docker
   make clean
   docker system prune -a
   ```

3. **Services not starting**
   ```bash
   # Check logs for errors
   make logs
   
   # Restart services
   make restart
   ```

4. **Database connection issues**
   ```bash
   # Ensure databases are running
   docker-compose ps
   
   # Check database logs
   docker-compose logs mongodb
   docker-compose logs elasticsearch
   docker-compose logs redis
   ```

### Reset Everything
```bash
# Complete reset (removes all data)
make clean

# Rebuild and start fresh
make build
make up
```

## 🔒 Security Notes

- Change default JWT_SECRET in production
- Use strong API keys
- Consider using Docker secrets for sensitive data
- Run containers as non-root users (already configured)

## 📈 Performance Tips

- Allocate sufficient RAM to Docker Desktop (4GB minimum)
- Use SSD storage for better I/O performance
- Monitor container resource usage with `docker stats`
