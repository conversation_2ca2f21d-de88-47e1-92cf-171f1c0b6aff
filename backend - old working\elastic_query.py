from elasticsearch import Elasticsearch

# Establish a connection to Elasticsearch
es = Elasticsearch("http://localhost:9200")  # Update if your Elasticsearch is running elsewhere

def run_query(query_body, index_name="search"):
    """
    Executes an Elasticsearch query and prints the results in a user-friendly format.

    Args:
        query_body (dict): The Elasticsearch query in dictionary format.
        index_name (str, optional): The name of the index to query. Defaults to "search".
    """
    try:
        response = es.search(index=index_name, query=query_body)
        print("\nQuery:")
        print(query_body)
        print("\nResults:")
        print(f"Total hits: {response['hits']['total']['value']}")
        for hit in response['hits']['hits']:
            # Use .get() to avoid KeyError if a field is missing in a document.
            source = hit.get('_source', {})
            print(f"  ID: {hit['_id']}, Name: {source.get('name')}, Category: {source.get('category')}, Color: {source.get('attributes', {}).get('color')}, Type: {source.get('type')}")
    except Exception as e:
        print(f"Error executing query: {e}")

def get_all_documents():
    """
    Retrieves all documents from the specified index.  Useful for understanding
    the data structure.
    """
    query = {"match_all": {}}
    run_query(query)

def search_by_name(query_string):
    """
    Performs a text search on the "name" field using the my_analyzer.
    """
    query = {
        "match": {
            "name": {
                "query": query_string,
                "analyzer": "my_analyzer"
            }
        }
    }
    run_query(query)

def search_by_name_keyword(query_string):
    """
    Performs an exact match search on the "name.keyword" field.
    """
    query = {
        "term": {
            "name.keyword": query_string
        }
    }
    run_query(query)

def filter_by_category(category_name):
    """
    Filters documents by the "category.keyword" field for an exact match.
    """
    query = {
        "term": {
            "category.keyword": category_name
        }
    }
    run_query(query)

def filter_by_category_hierarchy(category_hierarchy):
    """
    Filters documents by the "category.hierarchy" field for an exact match.
    """
    query = {
        "term":{
            "category.hierarchy": category_hierarchy
        }
    }
    run_query(query)

def search_description(query_string):
    """
    Performs a text search on the "description" field using the english analyzer.
    """
    query = {
        "match": {
            "description": query_string
        }
    }
    run_query(query)

def filter_by_color(color_name):
    """
    Filters documents by the "attributes.color.keyword" field.
    """
    query = {
        "term": {
            "attributes.color.keyword": color_name
        }
    }
    run_query(query)

def filter_by_type(item_type):
    """
    Filters documents by the "type" field.
    """
    query = {
        "term":{
            "type": item_type
        }
    }
    run_query(query)

def search_with_multiple_criteria(query_string, category_name, item_type):
    """
    Combines a text search with category and type filters.
    """
    query = {
        "bool": {
            "must": [
                {
                    "multi_match": {
                        "query": query_string,
                        "fields": ["name", "description"]
                    }
                }
            ],
            "filter": [
                {
                    "term": {
                        "category.keyword": category_name
                    }
                },
                {
                    "term":{
                        "type": item_type
                    }
                }
            ]
        }
    }
    run_query(query)

def main():
    """
    Main function to execute the query examples.
    """
    print("Running Elasticsearch Query Examples:")
    print("---------------------------------------")

    print("\nExample 1: Get All Documents")
    get_all_documents()

    print("\nExample 2: Search by Name (my_analyzer)")
    search_by_name("Red Headphone")

    print("\nExample 3: Search by Name Keyword (Exact Match)")
    search_by_name_keyword("Red Headphone")

    print("\nExample 4: Filter by Category")
    filter_by_category("Electronics")

    print("\nExample 5: Filter by Category Hierarchy")
    filter_by_category_hierarchy("Electronics")

    print("\nExample 6: Search Description")
    search_description("high quality sound")

    print("\nExample 7: Filter by Color")
    filter_by_color("Red")

    print("\nExample 8: Filter by Type")
    filter_by_type("product")

    print("\nExample 9: Search with Multiple Criteria")
    search_with_multiple_criteria("Headphone", "Electronics", "product")


if __name__ == "__main__":
    main()
