import React, { useState } from 'react';
import { 
  Bell, 
  ShoppingCart, 
  Wallet, 
  Sun,
  Moon,
  Globe2,
  Languages
} from 'lucide-react';

type CategoryType = 'all' | 'products' | 'services' | 'apps' | 'docs';

interface TabBarProps {
  isBusinessMode: boolean;
  onModeChange: (mode: boolean) => void;
  onSearch: (query: string) => void;
  selectedCategory: CategoryType;
  onCategoryChange: (category: CategoryType) => void;
}

const TabBar: React.FC<TabBarProps> = ({
  isBusinessMode,
  onModeChange,
  onSearch,
  selectedCategory,
  onCategoryChange
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-white">
      <div className="flex items-center space-x-4">
        {/* Mode Toggle */}
        <div className="flex items-center bg-gray-100 p-0.5 rounded-full">
          <button
            className={`px-4 py-1.5 rounded-full transition-colors text-sm font-medium ${
              !isBusinessMode
                ? 'bg-purple-400 text-white'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => onModeChange(false)}
          >
            Personal
          </button>
          <button
            className={`px-4 py-1.5 rounded-full transition-colors text-sm font-medium ${
              isBusinessMode
                ? 'bg-purple-400 text-white'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => onModeChange(true)}
          >
            Business
          </button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search..."
            className="w-[400px] px-4 py-2 rounded-full bg-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-purple-400"
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>
      </div>

      {/* Right Side Items */}
      <div className="flex items-center space-x-3">
        {/* Country Selector */}
        <div className="relative group">
          <button className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
            <Globe2 className="w-5 h-5" />
          </button>
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none group-hover:pointer-events-auto">
            <div className="py-2">
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">United States</div>
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">United Kingdom</div>
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Canada</div>
            </div>
          </div>
        </div>

        {/* Language Selector */}
        <div className="relative group">
          <button className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
            <Languages className="w-5 h-5" />
          </button>
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none group-hover:pointer-events-auto">
            <div className="py-2">
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">English</div>
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Spanish</div>
              <div className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">French</div>
            </div>
          </div>
        </div>

        {/* Theme Toggle */}
        <button 
          className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"
          onClick={() => setIsDarkMode(!isDarkMode)}
        >
          {isDarkMode ? <Moon className="w-5 h-5" /> : <Sun className="w-5 h-5" />}
        </button>

        {/* Cart */}
        <div className="relative group">
          <button className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
            <ShoppingCart className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
          </button>
        </div>

        {/* Wallet */}
        <button className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
          <Wallet className="w-5 h-5" />
        </button>

        {/* Notifications */}
        <div className="relative group">
          <button className="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">5</span>
          </button>
        </div>

        {/* SOS Button */}
        <button className="px-3 py-1 bg-red-500 text-white text-sm font-bold rounded-md hover:bg-red-600 transition-colors">
          SOS
        </button>

        {/* Profile */}
        <div className="relative group">
          <button className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full overflow-hidden">
              <img 
                src="/public/profile.jpg" 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            </div>
          </button>
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none group-hover:pointer-events-auto">
            <div className="p-4 border-b border-gray-200">
              <div className="font-medium text-gray-900">John Doe</div>
              <div className="text-sm text-gray-500"><EMAIL></div>
            </div>
            <div className="py-2">
              <a href="#profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
              <a href="#settings" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
              <a href="#logout" className="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Sign out</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TabBar;