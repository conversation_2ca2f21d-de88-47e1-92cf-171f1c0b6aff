#!/usr/bin/env python3
"""
Elasticsearch initialization script
Creates the search index and populates it with data from MongoDB
"""

import os
import sys
import time
from elasticsearch import Elasticsearch
from pymongo import MongoClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
ELASTICSEARCH_URL = os.getenv('ELASTICSEARCH_URL', 'http://elasticsearch:9200')
MONGODB_URL = os.getenv('MONGODB_URL', 'mongodb://mongodb:27017')
MONGODB_DB = os.getenv('MONGODB_DB', 'superapp')
ELASTICSEARCH_INDEX = os.getenv('ELASTICSEARCH_INDEX', 'search')

def wait_for_elasticsearch(es_client, max_retries=30, delay=2):
    """Wait for Elasticsearch to be ready"""
    for i in range(max_retries):
        try:
            if es_client.ping():
                logger.info("Elasticsearch is ready!")
                return True
        except Exception as e:
            logger.info(f"Waiting for Elasticsearch... (attempt {i+1}/{max_retries}): {e}")
            time.sleep(delay)
    return False

def wait_for_mongodb(mongo_client, max_retries=30, delay=2):
    """Wait for MongoDB to be ready"""
    for i in range(max_retries):
        try:
            mongo_client.admin.command('ping')
            logger.info("MongoDB is ready!")
            return True
        except Exception as e:
            logger.info(f"Waiting for MongoDB... (attempt {i+1}/{max_retries}): {e}")
            time.sleep(delay)
    return False

def create_search_index(es_client):
    """Create the search index with proper mapping"""

    # Check if index already exists
    if es_client.indices.exists(index=ELASTICSEARCH_INDEX):
        logger.info(f"Index '{ELASTICSEARCH_INDEX}' already exists, skipping creation")
        return True
    
    # Define the index mapping
    mapping = {
        "mappings": {
            "properties": {
                "id": {"type": "keyword"},
                "name": {"type": "text", "analyzer": "standard"},
                "description": {"type": "text", "analyzer": "standard"},
                "type": {"type": "keyword"},
                "category": {"type": "keyword"},
                "tags": {"type": "keyword"},
                "endpoint": {"type": "keyword"},
                "status": {"type": "keyword"},
                "created_at": {"type": "date"},
                "usage_count": {"type": "integer"},
                "location": {
                    "type": "geo_point"
                },
                "price": {
                    "properties": {
                        "amount": {"type": "float"},
                        "currency": {"type": "keyword"}
                    }
                },
                "rating": {
                    "properties": {
                        "average": {"type": "float"},
                        "count": {"type": "integer"}
                    }
                }
            }
        },
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 0
        }
    }
    
    try:
        es_client.indices.create(index=ELASTICSEARCH_INDEX, body=mapping)
        logger.info(f"Created index '{ELASTICSEARCH_INDEX}' with mapping")
        return True
    except Exception as e:
        logger.error(f"Failed to create index: {e}")
        return False

def sync_services_to_elasticsearch(mongo_client, es_client):
    """Sync services from MongoDB to Elasticsearch"""

    try:
        db = mongo_client[MONGODB_DB]
        services_collection = db.services

        # Get all services from MongoDB
        services = list(services_collection.find())
        logger.info(f"Found {len(services)} services in MongoDB")
        
        if not services:
            logger.warning("No services found in MongoDB to sync")
            return
        
        # Index each service in Elasticsearch
        for service in services:
            try:
                # Convert MongoDB ObjectId to string
                if '_id' in service:
                    del service['_id']  # Remove MongoDB ObjectId
                
                # Use the service id as the document id
                doc_id = service.get('id', str(service.get('_id')))

                es_client.index(
                    index=ELASTICSEARCH_INDEX,
                    id=doc_id,
                    body=service
                )
                logger.info(f"Indexed service: {service.get('name', 'Unknown')}")
                
            except Exception as e:
                logger.error(f"Failed to index service {service.get('name', 'Unknown')}: {e}")
        
        logger.info(f"Successfully synced {len(services)} services to Elasticsearch")
        
    except Exception as e:
        logger.error(f"Failed to sync services: {e}")

def main():
    """Main initialization function"""
    logger.info("Starting Elasticsearch initialization...")

    # Initialize clients with proper configuration for ES 8.x
    es_client = Elasticsearch(
        [ELASTICSEARCH_URL],
        verify_certs=False,
        ssl_show_warn=False
    )
    mongo_client = MongoClient(MONGODB_URL)

    try:
        # Wait for services to be ready
        logger.info("Waiting for Elasticsearch to be ready...")
        if not wait_for_elasticsearch(es_client):
            logger.error("Elasticsearch is not ready, exiting")
            return False

        logger.info("Waiting for MongoDB to be ready...")
        if not wait_for_mongodb(mongo_client):
            logger.error("MongoDB is not ready, exiting")
            return False

        # Create the search index
        logger.info("Creating search index...")
        if not create_search_index(es_client):
            logger.error("Failed to create search index")
            return False

        # Sync data from MongoDB
        logger.info("Syncing services from MongoDB to Elasticsearch...")
        sync_services_to_elasticsearch(mongo_client, es_client)

        logger.info("Elasticsearch initialization completed successfully!")
        return True

    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        return False

    finally:
        # Close connections
        es_client.close()
        mongo_client.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
