// TopNavigation.tsx
import React from 'react';

const TopNavigation = () => {
  return (
    <div className="h-12 bg-purple-400 text-white flex items-center px-4">
      <h1 className="text-lg font-semibold">SuperApp</h1>
    </div>
  );
};

export default TopNavigation;

const TabItem = ({ label, active = false }) => {
  return (
    <div className={`px-3 py-2 flex items-center space-x-1 border-b-2 ${active ? 'border-purple-400 text-purple-400' : 'border-transparent text-gray-500'}`}>
      <span>{label}</span>
      <XIcon className="w-4 h-4" />
    </div>
  );
};