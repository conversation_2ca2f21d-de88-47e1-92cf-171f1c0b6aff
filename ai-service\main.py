import os
import json
import base64
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Literal
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import redis.asyncio as redis
from datetime import datetime
import uuid
import io
from PIL import Image
import numpy as np
from bs4 import BeautifulSoup
import re
import aiohttp
from urllib.parse import urljoin

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="SuperApp AI Service", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
SARVAM_API_KEY = os.getenv("SARVAM_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
BACKEND_URL = os.getenv("BACKEND_URL", "http://localhost:8000")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
DEFAULT_MODEL = os.getenv("DEFAULT_MODEL", "gpt-3.5-turbo")
FALLBACK_MODEL = os.getenv("FALLBACK_MODEL", "gpt-3.5-turbo")
SARVAM_DEFAULT_MODEL = "sarvam-m"

# Redis client
redis_client = None

@app.on_event("startup")
async def startup_event():
    global redis_client
    try:
        redis_client = redis.from_url(REDIS_URL, decode_responses=True)
        await redis_client.ping()
        logger.info("Connected to Redis successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    if redis_client:
        await redis_client.close()

# AI Task Models
class AITaskRequest(BaseModel):
    task_type: str = Field(..., description="Type of AI task (text, image, audio, video, code)")
    input_data: str = Field(..., description="Input data for the task")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    user_id: Optional[str] = None

class AITaskResponse(BaseModel):
    task_id: str
    task_type: str
    status: str
    result: Optional[Any] = None
    error: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None

class TextTranslationRequest(BaseModel):
    text: str
    source_language: str = "auto"
    target_language: str = "en"

class ImageAnalysisRequest(BaseModel):
    image_data: str  # Base64 encoded image
    analysis_type: str = "general"  # general, objects, text, faces

class AudioProcessingRequest(BaseModel):
    audio_data: str  # Base64 encoded audio
    processing_type: str = "transcribe"  # transcribe, analyze, enhance

class VideoAnalysisRequest(BaseModel):
    video_data: str  # Base64 encoded video or URL
    analysis_type: str = "summary"  # summary, objects, scenes, audio

class CodeGenerationRequest(BaseModel):
    prompt: str
    language: str = "python"
    context: Optional[str] = None

class ChatMessage(BaseModel):
    role: Literal["user", "assistant", "system"]
    content: str
    metadata: Optional[Dict[str, Any]] = None

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    context: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    message: ChatMessage
    sources: Optional[List[Dict[str, str]]] = None
    actions: Optional[List[Dict[str, Any]]] = None

# AI Service Functions
async def translate_text(request: TextTranslationRequest) -> Dict[str, Any]:
    """Translate text using Sarvam AI"""
    try:
        if not SARVAM_API_KEY:
            return {"error": "Sarvam API key not configured"}
        
        # Mock translation for MVP - in production, use actual Sarvam API
        translated_text = f"[Translated from {request.source_language} to {request.target_language}] {request.text}"
        
        return {
            "translated_text": translated_text,
            "source_language": request.source_language,
            "target_language": request.target_language,
            "confidence": 0.95
        }
    except Exception as e:
        logger.error(f"Translation error: {e}")
        return {"error": str(e)}

async def analyze_image(request: ImageAnalysisRequest) -> Dict[str, Any]:
    """Analyze image content"""
    try:
        # Decode base64 image
        image_bytes = base64.b64decode(request.image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Mock image analysis - in production, use actual AI models
        analysis_results = {
            "dimensions": {"width": image.width, "height": image.height},
            "format": image.format,
            "mode": image.mode
        }
        
        if request.analysis_type == "general":
            analysis_results.update({
                "objects_detected": [
                    {"object": "person", "confidence": 0.89, "bbox": [100, 150, 300, 450]},
                    {"object": "car", "confidence": 0.76, "bbox": [200, 300, 500, 400]}
                ],
                "scene": "urban street",
                "dominant_colors": ["blue", "gray", "white"]
            })
        elif request.analysis_type == "text":
            analysis_results.update({
                "text_regions": [
                    {"text": "STOP", "bbox": [50, 50, 150, 100], "confidence": 0.95}
                ]
            })
        elif request.analysis_type == "faces":
            analysis_results.update({
                "faces_detected": [
                    {"bbox": [120, 180, 220, 280], "confidence": 0.92, "age": 25, "gender": "male"}
                ]
            })
        
        return analysis_results
    except Exception as e:
        logger.error(f"Image analysis error: {e}")
        return {"error": str(e)}

async def process_audio(request: AudioProcessingRequest) -> Dict[str, Any]:
    """Process audio content"""
    try:
        # Mock audio processing - in production, use actual AI models
        if request.processing_type == "transcribe":
            return {
                "transcript": "This is a mock transcription of the audio content.",
                "language": "en",
                "confidence": 0.87,
                "duration_seconds": 45.2
            }
        elif request.processing_type == "analyze":
            return {
                "sentiment": "positive",
                "emotion": "happy",
                "speaker_count": 2,
                "language": "en",
                "audio_quality": "good"
            }
        elif request.processing_type == "enhance":
            return {
                "enhanced_audio": request.audio_data,  # Mock - return same data
                "improvements": ["noise_reduction", "volume_normalization"],
                "quality_score": 8.5
            }
        
        return {"error": "Unknown processing type"}
    except Exception as e:
        logger.error(f"Audio processing error: {e}")
        return {"error": str(e)}

async def analyze_video(request: VideoAnalysisRequest) -> Dict[str, Any]:
    """Analyze video content"""
    try:
        # Mock video analysis - in production, use actual AI models
        if request.analysis_type == "summary":
            return {
                "summary": "This video shows a person walking through a city street with cars passing by.",
                "duration_seconds": 120.5,
                "key_scenes": [
                    {"timestamp": 0, "description": "Person starts walking"},
                    {"timestamp": 30, "description": "Cars passing by"},
                    {"timestamp": 90, "description": "Person reaches destination"}
                ],
                "dominant_objects": ["person", "car", "building", "street"]
            }
        elif request.analysis_type == "objects":
            return {
                "objects_timeline": [
                    {"timestamp": 0, "objects": ["person", "street"]},
                    {"timestamp": 15, "objects": ["person", "car", "street"]},
                    {"timestamp": 30, "objects": ["person", "building", "street"]}
                ]
            }
        elif request.analysis_type == "audio":
            return {
                "audio_analysis": {
                    "transcript": "Sounds of city traffic and footsteps.",
                    "sound_events": ["footsteps", "car_engine", "wind"],
                    "volume_levels": {"min": 0.2, "max": 0.8, "average": 0.5}
                }
            }
        
        return {"error": "Unknown analysis type"}
    except Exception as e:
        logger.error(f"Video analysis error: {e}")
        return {"error": str(e)}

async def generate_code(request: CodeGenerationRequest) -> Dict[str, Any]:
    """Generate code based on prompt"""
    try:
        # Mock code generation - in production, use actual AI models
        if request.language == "python":
            generated_code = f'''
def generated_function():
    """
    Generated function based on prompt: {request.prompt}
    """
    # TODO: Implement the actual logic
    return "This is a mock implementation"

# Example usage
if __name__ == "__main__":
    result = generated_function()
    print(result)
'''
        elif request.language == "javascript":
            generated_code = f'''
function generatedFunction() {{
    /**
     * Generated function based on prompt: {request.prompt}
     */
    // TODO: Implement the actual logic
    return "This is a mock implementation";
}}

// Example usage
console.log(generatedFunction());
'''
        else:
            generated_code = f"// Generated code for {request.language}\n// Prompt: {request.prompt}\n// TODO: Implement"
        
        return {
            "generated_code": generated_code,
            "language": request.language,
            "explanation": f"This code was generated based on the prompt: '{request.prompt}'. It includes basic structure and comments.",
            "suggestions": [
                "Add error handling",
                "Add input validation",
                "Add unit tests"
            ]
        }
    except Exception as e:
        logger.error(f"Code generation error: {e}")
        return {"error": str(e)}

async def search_web(query: str) -> List[Dict[str, str]]:
    """Search the web for information"""
    try:
        # In production, use a real search API (Google, Bing, etc.)
        # For now, return mock results
        return [
            {
                "url": "https://example.com/result1",
                "title": f"Search Result 1 for: {query}",
                "snippet": "This is a mock search result snippet..."
            },
            {
                "url": "https://example.com/result2",
                "title": f"Search Result 2 for: {query}",
                "snippet": "Another mock search result snippet..."
            }
        ]
    except Exception as e:
        logger.error(f"Web search error: {e}")
        return []

async def fetch_webpage(url: str) -> Optional[str]:
    """Fetch and extract content from a webpage"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()
                    
                    # Get text content
                    text = soup.get_text()
                    
                    # Clean up whitespace
                    lines = (line.strip() for line in text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    text = ' '.join(chunk for chunk in chunks if chunk)
                    
                    return text
                return None
    except Exception as e:
        logger.error(f"Error fetching webpage {url}: {e}")
        return None

async def execute_tool(tool: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """Execute a tool action"""
    try:
        if tool == "search":
            results = await search_web(params.get("query", ""))
            return {"results": results}
        elif tool == "browse":
            content = await fetch_webpage(params.get("url", ""))
            return {"content": content}
        elif tool == "calculate":
            # Simple calculator - in production use a more robust solution
            expression = params.get("expression", "")
            try:
                result = eval(expression, {"__builtins__": {}})
                return {"result": result}
            except:
                return {"error": "Invalid expression"}
        elif tool == "recommend":
            # Mock recommendations
            return {
                "recommendations": [
                    {"id": "1", "name": "Similar Product 1", "score": 0.95},
                    {"id": "2", "name": "Similar Product 2", "score": 0.85}
                ]
            }
        return {"error": "Unknown tool"}
    except Exception as e:
        logger.error(f"Tool execution error: {e}")
        return {"error": str(e)}

async def process_chat(request: ChatRequest) -> ChatResponse:
    """Process chat messages with browsing and tool capabilities"""
    try:
        logger.info(f"Processing chat request with {len(request.messages)} messages")

        if not OPENAI_API_KEY and not SARVAM_API_KEY:
            logger.error("No API keys configured")
            raise HTTPException(
                status_code=500,
                detail="No API key configured. Please set either OPENAI_API_KEY or SARVAM_API_KEY environment variable."
            )

        if not request.messages:
            logger.error("No messages provided in request")
            raise HTTPException(
                status_code=400,
                detail="No messages provided in the request"
            )

        logger.info(f"API Keys available - OpenAI: {'Yes' if OPENAI_API_KEY else 'No'}, Sarvam: {'Yes' if SARVAM_API_KEY else 'No'}")

        context = request.context or {}
        last_message = request.messages[-1].content if request.messages else ""
        
        # Track used tools and sources
        tools_used = []
        sources = []
        
        # Check if browsing is enabled
        if context.get("browsing"):
            # Search for relevant information
            search_results = await search_web(last_message)
            if search_results:
                sources.extend(search_results)
                # Fetch content from first result
                if search_results[0]["url"]:
                    content = await fetch_webpage(search_results[0]["url"])
                    if content:
                        tools_used.append("web_browse")
        
        # Check if tools are enabled
        if context.get("tools"):
            # Execute relevant tools based on message content
            if "calculate" in last_message.lower():
                # Extract expression - in production use better parsing
                match = re.search(r'\d+[\s\+\-\*\/\d\s]*\d+', last_message)
                if match:
                    calc_result = await execute_tool("calculate", {"expression": match.group()})
                    if "result" in calc_result:
                        tools_used.append("calculate")
        
        # Generate response using available API
        response_content = ""

        if OPENAI_API_KEY:
            # Use OpenAI API with OpenAI models
            openai_model = context.get("model", DEFAULT_MODEL)
            # Ensure we use a valid OpenAI model
            if openai_model == "sarvam-m":
                openai_model = DEFAULT_MODEL

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {OPENAI_API_KEY}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": openai_model,
                        "messages": [
                            {"role": msg.role, "content": msg.content}
                            for msg in request.messages
                        ],
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                if response.status_code == 200:
                    ai_response = response.json()
                    response_content = ai_response["choices"][0]["message"]["content"]
                else:
                    logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                    # Try Sarvam as fallback if available
                    if SARVAM_API_KEY:
                        logger.info("Falling back to Sarvam API")
                    else:
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"OpenAI API error: {response.text}"
                        )

        # Use Sarvam API if OpenAI failed or not available
        if not response_content and SARVAM_API_KEY:
            # Use Sarvam API with Sarvam models
            sarvam_model = context.get("model", SARVAM_DEFAULT_MODEL)
            # Ensure we use a valid Sarvam model
            if sarvam_model in ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]:
                sarvam_model = SARVAM_DEFAULT_MODEL

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.sarvam.ai/v1/chat/completions",
                    headers={
                        "api-subscription-key": SARVAM_API_KEY,
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": sarvam_model,
                        "messages": [
                            {"role": msg.role, "content": msg.content}
                            for msg in request.messages
                        ],
                        "temperature": 0.7
                    },
                    timeout=30.0
                )
                if response.status_code == 200:
                    ai_response = response.json()
                    response_content = ai_response["choices"][0]["message"]["content"]
                else:
                    logger.error(f"Sarvam API error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Sarvam API error: {response.text}"
                    )

        # If no response content, provide fallback
        if not response_content:
            response_content = "I apologize, but I'm currently unable to process your request due to API configuration issues. Please check that the API keys are properly configured."
        
        return ChatResponse(
            message=ChatMessage(
                role="assistant",
                content=response_content,
                metadata={"tools_used": tools_used}
            ),
            sources=sources if sources else None,
            actions=[{"type": "tool_result", "data": {"tools": tools_used}}] if tools_used else None
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Chat processing error: {e}")
        # Provide a more user-friendly error message
        error_msg = "I'm experiencing technical difficulties. Please try again in a moment."
        if "API" in str(e):
            error_msg = "I'm having trouble connecting to the AI service. Please check your API configuration."
        raise HTTPException(status_code=500, detail=error_msg)

# API Endpoints
@app.post("/execute", response_model=AITaskResponse)
async def execute_ai_task(request: AITaskRequest):
    """Execute AI task based on type"""
    task_id = str(uuid.uuid4())
    
    try:
        if request.task_type == "text":
            # Parse text request
            if request.parameters.get("operation") == "translate":
                text_req = TextTranslationRequest(
                    text=request.input_data,
                    source_language=request.parameters.get("source_language", "auto"),
                    target_language=request.parameters.get("target_language", "en")
                )
                result = await translate_text(text_req)
            else:
                # Default text processing
                result = {
                    "processed_text": request.input_data.upper(),
                    "word_count": len(request.input_data.split()),
                    "character_count": len(request.input_data)
                }
        
        elif request.task_type == "image":
            image_req = ImageAnalysisRequest(
                image_data=request.input_data,
                analysis_type=request.parameters.get("analysis_type", "general")
            )
            result = await analyze_image(image_req)
        
        elif request.task_type == "audio":
            audio_req = AudioProcessingRequest(
                audio_data=request.input_data,
                processing_type=request.parameters.get("processing_type", "transcribe")
            )
            result = await process_audio(audio_req)
        
        elif request.task_type == "video":
            video_req = VideoAnalysisRequest(
                video_data=request.input_data,
                analysis_type=request.parameters.get("analysis_type", "summary")
            )
            result = await analyze_video(video_req)
        
        elif request.task_type == "code":
            code_req = CodeGenerationRequest(
                prompt=request.input_data,
                language=request.parameters.get("language", "python"),
                context=request.parameters.get("context")
            )
            result = await generate_code(code_req)
        
        else:
            raise HTTPException(status_code=400, detail=f"Unknown task type: {request.task_type}")
        
        # Cache result in Redis if available
        if redis_client and "error" not in result:
            await redis_client.setex(
                f"ai_task:{task_id}",
                3600,  # 1 hour TTL
                json.dumps(result, default=str)
            )
        
        return AITaskResponse(
            task_id=task_id,
            task_type=request.task_type,
            status="completed" if "error" not in result else "failed",
            result=result,
            error=result.get("error"),
            completed_at=datetime.utcnow()
        )
    
    except Exception as e:
        logger.error(f"AI task execution error: {e}")
        return AITaskResponse(
            task_id=task_id,
            task_type=request.task_type,
            status="failed",
            error=str(e),
            completed_at=datetime.utcnow()
        )

@app.get("/task/{task_id}")
async def get_task_result(task_id: str):
    """Get cached task result"""
    if not redis_client:
        raise HTTPException(status_code=503, detail="Cache service unavailable")
    
    try:
        result = await redis_client.get(f"ai_task:{task_id}")
        if result:
            return {"task_id": task_id, "result": json.loads(result)}
        else:
            raise HTTPException(status_code=404, detail="Task not found")
    except Exception as e:
        logger.error(f"Error retrieving task: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/translate")
async def translate_text_endpoint(request: TextTranslationRequest):
    """Direct text translation endpoint"""
    result = await translate_text(request)
    return result

@app.post("/analyze-image")
async def analyze_image_endpoint(request: ImageAnalysisRequest):
    """Direct image analysis endpoint"""
    result = await analyze_image(request)
    return result

@app.post("/process-audio")
async def process_audio_endpoint(request: AudioProcessingRequest):
    """Direct audio processing endpoint"""
    result = await process_audio(request)
    return result

@app.post("/analyze-video")
async def analyze_video_endpoint(request: VideoAnalysisRequest):
    """Direct video analysis endpoint"""
    result = await analyze_video(request)
    return result

@app.post("/generate-code")
async def generate_code_endpoint(request: CodeGenerationRequest):
    """Direct code generation endpoint"""
    result = await generate_code(request)
    return result

@app.post("/upload-file")
async def upload_file(file: UploadFile = File(...), task_type: str = Form(...)):
    """Upload file for processing"""
    try:
        # Read file content
        content = await file.read()
        
        # Convert to base64 for processing
        base64_content = base64.b64encode(content).decode('utf-8')
        
        # Create AI task request
        ai_request = AITaskRequest(
            task_type=task_type,
            input_data=base64_content,
            parameters={"filename": file.filename, "content_type": file.content_type}
        )
        
        # Execute task
        result = await execute_ai_task(ai_request)
        return result
    
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Chat endpoint with browsing and tool capabilities"""
    return await process_chat(request)

@app.get("/capabilities")
async def get_capabilities():
    """Get AI service capabilities"""
    return {
        "version": "1.0.0",
        "services": {
            "chat": {
                "models": ["sarvam-m","gpt-4", "gpt-3.5-turbo", "claude-3-opus", "claude-3-sonnet"],
                "features": {
                    "browsing": True,
                    "tools": ["search", "browse", "calculate", "recommend"],
                    "mcp": True,
                    "agentic": True
                }
            },
            "translation": {
                "supported_languages": ["en", "es", "fr", "de", "zh", "ja", "ko"],
                "features": ["auto_detect", "batch_translation"]
            },
            "image_analysis": {
                "types": ["general", "objects", "text", "faces"],
                "formats": ["jpg", "png", "webp"]
            },
            "audio_processing": {
                "types": ["transcribe", "analyze", "enhance"],
                "formats": ["mp3", "wav", "ogg"]
            },
            "video_analysis": {
                "types": ["summary", "objects", "scenes", "audio"],
                "formats": ["mp4", "webm"]
            },
            "code_generation": {
                "languages": ["python", "javascript", "typescript", "java", "cpp"],
                "features": ["completion", "explanation", "optimization"]
            }
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "redis": "connected" if redis_client else "disconnected",
            "sarvam_api": "configured" if SARVAM_API_KEY else "not_configured"
        }
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SuperApp AI Service",
        "version": "1.0.0",
        "description": "Multimodal AI service for text, image, audio, video, and code processing",
        "endpoints": {
            "execute": "/execute",
            "translate": "/translate",
            "analyze_image": "/analyze-image",
            "process_audio": "/process-audio",
            "analyze_video": "/analyze-video",
            "generate_code": "/generate-code",
            "upload_file": "/upload-file",
            "capabilities": "/capabilities"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
