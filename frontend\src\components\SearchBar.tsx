import React, { useState, useCallback } from 'react';
import api from '../services/api';

interface SearchBarProps {
  className?: string;
  onSearch: (query: string) => void;
  hasResults?: boolean;
  selectedCategory?: 'all' | 'products' | 'services' | 'apps' | 'docs';
  onCategoryChange?: (category: 'all' | 'products' | 'services' | 'apps' | 'docs') => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ 
  className = '', 
  onSearch, 
  hasResults = false,
  selectedCategory = 'all',
  onCategoryChange
}) => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSearch = useCallback(async () => {
    if (!query.trim()) return;
    onSearch(query.trim());
  }, [query, onSearch]);

  const searchCategories = [
    { id: 'all' as const, label: 'All', icon: 'M4 6h16M4 12h16M4 18h16' },
    { id: 'products' as const, label: 'Products', icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' },
    { id: 'services' as const, label: 'Services', icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01' },
    { id: 'apps' as const, label: 'Apps', icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' },
    { id: 'docs' as const, label: 'Documentation', icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253' }
  ];

  return (
    <div className={`search-bar-container ${className}`}>
      <div className="search-input-container flex items-center bg-gray-100 rounded-full">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg className="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="w-full py-1.5 pl-9 pr-4 text-sm text-gray-700 bg-transparent rounded-l-full focus:outline-none"
            placeholder="Search anything..."
          />
        </div>
        <button 
          onClick={handleSearch}
          disabled={loading}
          className="py-1.5 px-4 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-200 rounded-r-full transition-colors focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? '...' : 'Search'}
        </button>
      </div>
      
      {hasResults && (
        <div className="category-tabs flex mt-4 space-x-2 overflow-x-auto pb-2">
          {searchCategories.map(category => (
            <button
              key={category.id}
              onClick={() => onCategoryChange?.(category.id)}
              className={`px-6 py-2 font-medium rounded-full focus:outline-none transition-colors whitespace-nowrap ${
                selectedCategory === category.id
                  ? 'bg-purple-400 text-white'
                  : 'bg-purple-100 text-purple-700 hover:bg-purple-200 border border-purple-200'
              }`}
            >
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={category.icon} />
                </svg>
                {category.label}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
