import React, { useState } from 'react';
import {
  BookOpen,
  DollarSign,
  FileText,
  Users,
  ShoppingCart,
  Mail,
  Globe,
  Briefcase,
  Box,
  Layout
} from 'lucide-react';

// Define sub-items for each business category
const subMenuItems: Record<string, Array<{ id: string; label: string; icon: React.ReactNode; description?: string }>> = {
  finance: [
    { id: 'accounting', label: 'Accounting', icon: <BookOpen size={20} />, description: 'Manage your books and financial records' },
    { id: 'invoicing', label: 'Invoicing', icon: <FileText size={20} />, description: 'Create and manage invoices' },
    { id: 'expenses', label: 'Expenses', icon: <DollarSign size={20} />, description: 'Track and manage expenses' }
  ],
  hr: [
    { id: 'employees', label: 'Employees', icon: <Users size={20} />, description: 'Manage employee records and information' },
    { id: 'recruitment', label: 'Recruitment', icon: <Users size={20} />, description: 'Handle recruitment and hiring processes' }
  ],
  sales: [
    { id: 'crm', label: 'CRM', icon: <Users size={20} />, description: 'Customer Relationship Management' },
    { id: 'pos', label: 'Point of Sale', icon: <ShoppingCart size={20} />, description: 'Manage sales and transactions' }
  ],
  marketing: [
    { id: 'email', label: 'Email Marketing', icon: <Mail size={20} />, description: 'Create and manage email campaigns' },
    { id: 'social', label: 'Social Marketing', icon: <Globe size={20} />, description: 'Manage social media marketing' }
  ],
  services: [
    { id: 'projects', label: 'Projects', icon: <Briefcase size={20} />, description: 'Manage projects and tasks' },
    { id: 'helpdesk', label: 'Helpdesk', icon: <Users size={20} />, description: 'Customer support and ticketing' }
  ],
  supply: [
    { id: 'inventory', label: 'Inventory', icon: <Box size={20} />, description: 'Manage stock and inventory' },
    { id: 'purchase', label: 'Purchase', icon: <ShoppingCart size={20} />, description: 'Handle purchase orders and suppliers' }
  ]
};

interface SubMenuProps {
  category: string | null;
  onSubItemSelect?: (subItem: string) => void;
}

const SubMenu: React.FC<SubMenuProps> = ({ category, onSubItemSelect }) => {
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  if (!category || !subMenuItems[category]) {
    return null;
  }

  const handleItemClick = (itemId: string) => {
    setSelectedItem(itemId);
    onSubItemSelect?.(itemId);
  };

  return (
    <div className="w-64 h-full bg-gray-50 border-r border-gray-200">
      <div className="p-4">
        <h2 className="text-sm font-semibold text-gray-600 uppercase tracking-wider mb-4">
          {category}
        </h2>
        <nav>
          <ul className="space-y-2">
            {subMenuItems[category].map((item) => (
              <li key={item.id}>
                <div className="relative group">
                  <button
                    onClick={() => handleItemClick(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-2 rounded-lg ${
                      selectedItem === item.id
                        ? 'bg-purple-50 text-purple-400'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span className="text-current">{item.icon}</span>
                    <span className="text-sm">{item.label}</span>
                  </button>
                  {item.description && (
                    <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 pointer-events-none z-50">
                      <div className="bg-gray-900 text-white text-sm px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        {item.description}
                      </div>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default SubMenu; 