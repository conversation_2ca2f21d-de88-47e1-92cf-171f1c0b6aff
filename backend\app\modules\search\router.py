from fastapi import APIRouter, HTTPException, Query
from typing import Optional
from app.modules.search.schemas import (
    SearchRequest,
    SearchResponse,
    Item,
    GeoCoordinate,
    SearchType
)
from app.modules.search.service import SearchService

router = APIRouter()
search_service = SearchService()

@router.get("/", response_model=SearchResponse)
async def search(
    query: str = Query(..., description="Search query"),
    type: SearchType = Query(SearchType.ALL, description="Type of items to search"),
    lat: Optional[float] = Query(None, ge=-90, le=90, description="Latitude for location-based search"),
    lng: Optional[float] = Query(None, ge=-180, le=180, description="Longitude for location-based search"),
    radius_km: Optional[float] = Query(None, ge=0, description="Search radius in kilometers"),
    h3_resolution: int = Query(9, ge=0, le=15, description="H3 resolution level (0-15)"),
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page")
) -> SearchResponse:
    """
    Search for products and services with optional geospatial filtering using H3 indexes
    """
    print(f"=== ROUTER CALLED with query: {query} ===")
    try:
        # Create search request
        location = GeoCoordinate(lat=lat, lng=lng) if lat is not None and lng is not None else None
        
        search_request = SearchRequest(
            query=query,
            location=location,
            type=type,
            radius_km=radius_km,
            h3_resolution=h3_resolution,
            page=page,
            per_page=per_page
        )
        
        # Perform search
        return await search_service.search(search_request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/index", response_model=bool)
async def index_item(item: Item) -> bool:
    """
    Index a single item with H3 geospatial index
    """
    try:
        return await search_service.index_item(item)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reindex")
async def reindex_all() -> dict:
    """
    Reindex all items with H3 indexes
    """
    try:
        success, errors = await search_service.reindex_all()
        return {
            "status": "completed",
            "success_count": success,
            "error_count": errors
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 