import requests

# Base URL of your FastAPI application
base_url = "http://localhost:8000"

def search_api(query: str, category: str = None, location: str = None, item_type: str = None, from_: int = 0, size: int = 10):
    """
    Queries the /api/search endpoint of the FastAPI application.

    Args:
        query: The search query string (required).
        category:  Filter by category (optional).
        location: Filter by location (optional).
        item_type: Filter by item type (store, product, service) (optional).
        from_:  Starting offset for results (optional, default 0).
        size: Number of results per page (optional, default 10).

    Returns:
        The JSON response from the /api/search endpoint.  Raises an exception
        if the request fails.
    """
    url = f"{base_url}/api/search"
    params = {
        "query": query,
        "category": category,
        "location": location,
        "item_type": item_type,
        "from": from_,
        "size": size
    }

    # Remove None values from the parameters, so they are not included in the request
    params = {k: v for k, v in params.items() if v is not None}

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error during request: {e}")
        return None  # Explicitly return None in case of an error.

def main():
    """
    Main function to demonstrate querying the /api/search endpoint.
    """
    # Example usage 1: Basic search
    print("Example 1: Basic Search")
    search_results = search_api(query="headphone")  # Replace "example" with a real query
    if search_results:
        print(f"Total hits: {search_results['total']}")
        print("Items:")
        for item in search_results['items']:
            print(item)

    # Example usage 2: Search with category filter
    print("\nExample 2: Search with Category Filter")
    search_results_category = search_api(query="headphones", category="headphone")  # Replace with actual values
    if search_results_category:
        print(f"Total hits: {search_results_category['total']}")
        print("Items:")
        for item in search_results_category['items']:
            print(item)

    # Example usage 3: Search with location and item_type
    print("\nExample 3: Search with Location and Item Type")
    search_results_location_type = search_api(query="headphone", location="60001", item_type="product")  # Replace
    if search_results_location_type:
        print(f"Total hits: {search_results_location_type['total']}")
        print("Items:")
        for item in search_results_location_type['items']:
            print(item)
    
    # Example usage 4: show the query being sent.
    print("\nExample 4: show the query being sent")
    search_results_show_query = search_api(query="headphone")
    if search_results_show_query:
        print(f"Total hits: {search_results_show_query['total']}")
        print("Items:")
        for item in search_results_show_query['items']:
            print(item)

if __name__ == "__main__":
    main()

