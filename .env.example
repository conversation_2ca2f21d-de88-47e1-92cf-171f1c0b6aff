# SuperApp Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
NODE_ENV=production
ENVIRONMENT=production

# =============================================================================
# API KEYS (Required for AI features)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
SARVAM_API_KEY=your_sarvam_api_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URL=mongodb://mongodb:27017/superapp
ELASTICSEARCH_URL=http://elasticsearch:9200
REDIS_URL=redis://redis:6379

# =============================================================================
# SERVICE URLS (Internal Docker networking)
# =============================================================================
BACKEND_URL=http://backend:8000
AI_SERVICE_URL=http://ai-service:8001

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_API_URL=http://localhost:8000
VITE_AI_SERVICE_URL=http://localhost:8001

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
CORS_ORIGINS=http://localhost,http://localhost:3000,http://localhost:80

# =============================================================================
# OPTIONAL: ADVANCED CONFIGURATION
# =============================================================================
# Database names
MONGODB_DATABASE=superapp
ELASTICSEARCH_INDEX=superapp

# Logging
LOG_LEVEL=INFO

# Performance
WORKERS=1
