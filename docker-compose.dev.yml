services:
  frontend:
    build:
      target: development
    command: npm run dev -- --host 0.0.0.0
    ports:
      - "3000:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8000
      - VITE_AI_SERVICE_URL=http://localhost:8001
    volumes:
      - ./frontend:/app
      - /app/node_modules
    restart: "no"

  backend:
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./backend:/app
    restart: "no"

  ai-service:
    command: uvicorn main:app --host 0.0.0.0 --port 8001 --reload
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./ai-service:/app
    restart: "no"

  api-gateway:
    command: uvicorn main:app --host 0.0.0.0 --port 8002 --reload
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./api-gateway:/app
    restart: "no"
