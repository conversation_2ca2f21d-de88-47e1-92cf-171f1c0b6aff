// ToggleSwitch.tsx
const ToggleSwitch = () => {
  const [active, setActive] = useState('personal');
  
  return (
    <div className="flex bg-gray-100 p-0.5 rounded-full">
      <button
        className={`px-5 py-2 rounded-full text-sm ${active === 'personal' ? 'bg-purple-400 text-white' : 'text-gray-700'}`}
        onClick={() => setActive('personal')}
      >
        Personal
      </button>
      <button
        className={`px-5 py-2 rounded-full text-sm ${active === 'business' ? 'bg-purple-400 text-white' : 'text-gray-700'}`}
        onClick={() => setActive('business')}
      >
        Business
      </button>
    </div>
  );
};