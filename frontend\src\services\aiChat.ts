import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: {
    source?: string;
    confidence?: number;
    tools_used?: string[];
  };
}

export interface ChatResponse {
  message: ChatMessage;
  sources?: {
    url: string;
    title: string;
    snippet: string;
  }[];
  actions?: {
    type: string;
    data: any;
  }[];
}

const aiApi = axios.create({
  baseURL: import.meta.env.VITE_AI_SERVICE_URL || 'http://localhost:8001', // AI service URL
  headers: {
    'Content-Type': 'application/json',
  },
});

export const aiChat = {
  // Send a message to the AI
  sendMessage: async (
    messages: ChatMessage[],
    context?: {
      item?: any;
      browsing?: boolean;
      tools?: string[];
    }
  ): Promise<ChatResponse> => {
    if (!messages || messages.length === 0) {
      throw new Error('Messages array cannot be empty');
    }

    try {
      console.log('Making AI chat request to:', aiApi.defaults.baseURL + '/chat');
      console.log('Request payload:', {
        messages,
        context: {
          ...context,
          capabilities: {
            browsing: true,
            tools: true,
            mcp: true,
            agentic: true
          }
        }
      });

      const response = await aiApi.post('/chat', {
        messages,
        context: {
          ...context,
          capabilities: {
            browsing: true,
            tools: true,
            mcp: true,
            agentic: true
          }
        }
      });

      console.log('AI chat response:', response.data);
      return response.data;
    } catch (error) {
      console.error('AI Chat error:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
        console.error('Error status:', error.response.status);
      }
      throw error;
    }
  },

  // Get product recommendations
  getRecommendations: async (item: any, preferences?: string[]): Promise<any[]> => {
    try {
      const response = await aiApi.post('/recommendations', {
        item,
        preferences
      });
      return response.data;
    } catch (error) {
      console.error('Recommendations error:', error);
      throw error;
    }
  },

  // Get product insights
  getInsights: async (item: any): Promise<any> => {
    try {
      const response = await aiApi.post('/insights', {
        item
      });
      return response.data;
    } catch (error) {
      console.error('Insights error:', error);
      throw error;
    }
  },

  // Execute a tool action
  executeTool: async (tool: string, params: any): Promise<any> => {
    try {
      const response = await aiApi.post('/execute-tool', {
        tool,
        params
      });
      return response.data;
    } catch (error) {
      console.error('Tool execution error:', error);
      throw error;
    }
  }
};

export default aiChat;