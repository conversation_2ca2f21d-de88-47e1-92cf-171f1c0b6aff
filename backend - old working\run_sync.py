import asyncio
import sys
from backend.main_backup import mongo_client, es_client
from sync_service import SyncService
import logging
import platform

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def main():
    try:
        # Initialize sync service
        sync_service = SyncService(mongo_client, es_client)
        
        # Perform initial sync of existing documents
        logger.info("Starting initial sync...")
        await sync_service.initial_sync()
        logger.info("Initial sync completed")
        
        # Start watching collections for changes
        logger.info("Starting change streams...")
        await sync_service.watch_collections()
        
    except Exception as e:
        logger.error(f"Error in sync service: {str(e)}")
        raise

if __name__ == "__main__":
    if sys.platform == "win32":
        # Use SelectorEventLoop for Windows to support aiodns
        loop = asyncio.SelectorEventLoop()
        asyncio.set_event_loop(loop)
    else:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(main())
    finally:
        loop.close()