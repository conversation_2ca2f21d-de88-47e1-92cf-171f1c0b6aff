#!/bin/bash

# Elasticsearch initialization script using curl
# Creates the search index and populates it with data from MongoDB

set -e

# Configuration
ELASTICSEARCH_URL=${ELASTICSEARCH_URL:-"http://elasticsearch:9200"}
MONGODB_URL=${MONGODB_URL:-"mongodb://mongodb:27017"}
MONGODB_DB=${MONGODB_DB:-"superapp"}
ELASTICSEARCH_INDEX=${ELASTICSEARCH_INDEX:-"search"}

echo "Starting Elasticsearch initialization..."

# Wait for Elasticsearch to be ready
echo "Waiting for Elasticsearch to be ready..."
for i in {1..30}; do
    if curl -s -f "$ELASTICSEARCH_URL" > /dev/null 2>&1; then
        echo "Elasticsearch is ready!"
        break
    fi
    echo "Waiting for Elasticsearch... (attempt $i/30)"
    sleep 2
done

# Check if Elasticsearch is ready
if ! curl -s -f "$ELASTICSEARCH_URL" > /dev/null 2>&1; then
    echo "ERROR: Elasticsearch is not ready, exiting"
    exit 1
fi

# Check if index already exists
if curl -s -f "$ELASTICSEARCH_URL/$ELASTICSEAR<PERSON>_INDEX" > /dev/null 2>&1; then
    echo "Index '$ELASTICSEARCH_INDEX' already exists, skipping creation"
else
    echo "Creating search index..."
    
    # Create the index with mapping
    curl -X PUT "$ELASTICSEARCH_URL/$ELASTICSEARCH_INDEX" \
        -H "Content-Type: application/json" \
        -d '{
            "mappings": {
                "properties": {
                    "id": {"type": "keyword"},
                    "name": {"type": "text", "analyzer": "standard"},
                    "description": {"type": "text", "analyzer": "standard"},
                    "type": {"type": "keyword"},
                    "category": {"type": "keyword"},
                    "tags": {"type": "keyword"},
                    "endpoint": {"type": "keyword"},
                    "status": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "usage_count": {"type": "integer"},
                    "location": {
                        "type": "geo_point"
                    },
                    "price": {
                        "properties": {
                            "amount": {"type": "float"},
                            "currency": {"type": "keyword"}
                        }
                    },
                    "rating": {
                        "properties": {
                            "average": {"type": "float"},
                            "count": {"type": "integer"}
                        }
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }'
    
    if [ $? -eq 0 ]; then
        echo "Successfully created index '$ELASTICSEARCH_INDEX'"
    else
        echo "ERROR: Failed to create index"
        exit 1
    fi
fi

echo "Elasticsearch initialization completed successfully!"
echo "Index '$ELASTICSEARCH_INDEX' is ready for use."

# Note: Data syncing from MongoDB to Elasticsearch would be handled by the backend application
# or a separate data pipeline. For now, we just ensure the index exists.

exit 0
