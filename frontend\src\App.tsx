import { useState, useEffect } from 'react';
import './App.css';
import TabBar from './components/TabBar';
import VerticalMenu from './components/VerticalMenu';
import AddressBar from './components/AddressBar';
import StatusBar from './components/StatusBar';
import AIInputBar from './components/AIInputBar';
import ChatInterface from './components/ChatInterface';
import AIChatInterface from './components/AIChatInterface';
import api, { getItemDetails, Item } from './services/api';
import ProductCard from './components/ProductCard';
import ItemDetailsModal from './components/ItemDetailsModal';
import SearchFilters, { FilterState } from './components/SearchFilters';

type CategoryType = 'all' | 'products' | 'services' | 'apps' | 'docs';

// Additional search result categories
type SearchCategoryType = CategoryType | 'web' | 'maps' | 'images' | 'videos';

interface SearchResult extends Item {}

function App() {
  // State management
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [showUserChat, setShowUserChat] = useState(() => {
    // Only open if explicitly saved in localStorage and AI chat is not open
    const userChatOpen = localStorage.getItem('showUserChat') === 'true';
    const aiChatOpen = localStorage.getItem('showAIChat') === 'true';
    return userChatOpen && !aiChatOpen;
  });
  const [showAIChat, setShowAIChat] = useState(() => {
    // Only open if explicitly saved in localStorage
    return localStorage.getItem('showAIChat') === 'true';
  });
  const [isBusinessMode, setIsBusinessMode] = useState(false);
  const [selectedSearchCategory] = useState<CategoryType>('all');
  const [filters, setFilters] = useState<FilterState>({
    activeButton: null,
    sortBy: undefined,
    filters: {
      inStock: false,
      onSale: false
    },
    priceRange: {
      min: null,
      max: null
    },
    rating: null
  });

  // State for active search category tab
  const [activeSearchTab, setActiveSearchTab] = useState<SearchCategoryType>('products');

  // Update main container margin when chat window state changes
  useEffect(() => {
    const mainContainer = document.querySelector('.main-content-container') as HTMLElement;
    if (mainContainer) {
      // Since chats are mutually exclusive, only one can be open at a time
      mainContainer.style.marginRight = (showAIChat || showUserChat) ? '400px' : '0';
    }
  }, [showAIChat, showUserChat]);

  // Save chat window states to localStorage
  useEffect(() => {
    localStorage.setItem('showUserChat', showUserChat.toString());
    // If user chat opens, ensure AI chat is closed in localStorage
    if (showUserChat) {
      localStorage.setItem('showAIChat', 'false');
    }
  }, [showUserChat]);

  useEffect(() => {
    localStorage.setItem('showAIChat', showAIChat.toString());
    // If AI chat opens, ensure user chat is closed in localStorage
    if (showAIChat) {
      localStorage.setItem('showUserChat', 'false');
    }
  }, [showAIChat]);

  // Load initial data when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      setSearchLoading(true);
      try {
        // Load some default services by searching with a common term
        const response = await api.search({
          query: 'service',  // Search for services to get initial data
          type: 'all',
          per_page: 20
        });
        setSearchResults(response.items || []);
      } catch (error) {
        console.error('Initial data loading error:', error);
        setSearchResults([]);
      } finally {
        setSearchLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handler for search query
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      // If empty query, reload initial data
      const response = await api.search({
        query: 'service',
        type: selectedSearchCategory,
        per_page: 20
      });
      setSearchResults(response.items || []);
      return;
    }

    setSearchQuery(query);
    setSearchLoading(true);
    try {
      const response = await api.search({
        query,
        type: selectedSearchCategory
      });
      // The API returns a SearchResponse object with items property
      setSearchResults(response.items || []);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  // Handler for viewing item details
  const handleViewDetails = async (item: Item) => {
    setLoading(true);
    try {
      const details = await getItemDetails(item.id);
      setSelectedItem(details);
      setModalOpen(true);
    } catch (error) {
      console.error('Error fetching item details:', error);
    } finally {
      setLoading(false);
    }
  };



  // Category display names mapping
  const categoryDisplayNames: Record<string, string> = {
    products: 'Products',
    services: 'Services',
    apps: 'Apps & Tools',
    docs: 'Documentation',
    web: 'Web',
    maps: 'Maps',
    images: 'Images',
    videos: 'Videos'
  };

  // Function to group results by category
  const groupResultsByCategory = (results: SearchResult[]) => {
    const grouped: Record<SearchCategoryType, SearchResult[]> = {
      products: [],
      services: [],
      apps: [],
      docs: [],
      web: [],
      maps: [],
      images: [],
      videos: [],
      all: []
    };

    results.forEach(result => {
      // Add to 'all' category
      grouped.all.push(result);

      // Check if the item has app-related tags
      const isAppLike = result.tags?.some(tag => 
        ['app', 'plugin', 'tool', 'mini-app'].includes(tag.toLowerCase())
      );

      // Check for media type tags
      const hasWebTag = result.tags?.some(tag => tag.toLowerCase().includes('web'));
      const hasMapTag = result.tags?.some(tag => tag.toLowerCase().includes('map'));
      const hasImageTag = result.tags?.some(tag => tag.toLowerCase().includes('image'));
      const hasVideoTag = result.tags?.some(tag => tag.toLowerCase().includes('video'));

      if (isAppLike) {
        grouped.apps.push(result);
      } else if (hasWebTag) {
        grouped.web.push(result);
      } else if (hasMapTag) {
        grouped.maps.push(result);
      } else if (hasImageTag) {
        grouped.images.push(result);
      } else if (hasVideoTag) {
        grouped.videos.push(result);
      } else {
        switch (result.type) {
          case 'product':
            grouped.products.push(result);
            break;
          case 'service':
            grouped.services.push(result);
            break;
          default:
            // Try to determine category from category array
            if (result.category?.some(cat => Object.keys(grouped).includes(cat))) {
              result.category.forEach(cat => {
                if (grouped[cat as SearchCategoryType]) {
                  grouped[cat as SearchCategoryType].push(result);
                }
              });
            } else {
              grouped.products.push(result); // Default to Products if no category found
            }
        }
      }
    });

    return grouped;
  };

  // Apply filters and sorting to results
  const applyFiltersAndSort = (results: SearchResult[]) => {
    let filteredResults = [...results];
    console.log('Applying filters:', filters); // Debug log

    // Apply filters
    if (filters.filters.inStock) {
      filteredResults = filteredResults.filter(item => (item.price?.amount ?? 0) > 0);
    }
    if (filters.filters.onSale) {
      filteredResults = filteredResults.filter(item => item.price?.discount_percentage && item.price.discount_percentage > 0);
    }

    // Apply price range filter
    if (filters.priceRange.min !== null || filters.priceRange.max !== null) {
      filteredResults = filteredResults.filter(item => {
        const price = item.price?.amount;
        if (!price) return false;
        if (filters.priceRange.min !== null && price < filters.priceRange.min) return false;
        if (filters.priceRange.max !== null && price > filters.priceRange.max) return false;
        return true;
      });
    }

    // Apply rating filter
    if (filters.rating !== null) {
      filteredResults = filteredResults.filter(item => {
        const rating = item.rating?.average;
        return rating ? rating >= filters.rating! : false;
      });
    }

    // Apply sorting
    if (filters.sortBy) {
      console.log('Sorting by:', filters.sortBy); // Debug log
      filteredResults.sort((a, b) => {
        switch (filters.sortBy) {
          case 'name_asc':
            return a.name.localeCompare(b.name);
          case 'name_desc':
            return b.name.localeCompare(a.name);
          case 'price_asc':
            return (a.price?.amount ?? 0) - (b.price?.amount ?? 0);
          case 'price_desc':
            return (b.price?.amount ?? 0) - (a.price?.amount ?? 0);
          default:
            return 0;
        }
      });
      console.log('Sorted results:', filteredResults.map(r => ({ name: r.name, price: r.price?.amount }))); // Debug log
    }

    // No need for additional filtering and sorting here since it's already done above

    return filteredResults;
  };

  return (
    <div className="fixed inset-0 flex flex-col">
      {/* Address Bar */}
      <div className="flex-none">
        <AddressBar />
      </div>

      {/* Tab Bar */}
      <TabBar
        isBusinessMode={isBusinessMode}
        onModeChange={setIsBusinessMode}
        onSearch={handleSearch}
      />

      {/* Main Content Area */}
      <div className="flex-1 grid grid-cols-[auto_auto_auto_1fr] min-h-0 relative main-content-container transition-all duration-300" style={{ height: 'calc(100vh - 140px)' }}>
        {/* First Vertical Navigation */}
        <VerticalMenu
          isBusinessMode={isBusinessMode}
          onCategorySelect={(category) => console.log('Selected category 1:', category)}
        />

        {/* Second Vertical Navigation */}
        <VerticalMenu
          isBusinessMode={isBusinessMode}
          onCategorySelect={(category) => console.log('Selected category 2:', category)}
        />

        {/* Third Vertical Navigation */}
        <VerticalMenu
          isBusinessMode={isBusinessMode}
          onCategorySelect={(category) => console.log('Selected category 3:', category)}
        />

        {/* Content Area */}
        <div className="bg-gray-50 overflow-auto content-area">
          <div className="max-w-7xl mx-auto p-6">
            {searchResults.length > 0 ? (
              <div>
                {/* Search Results */}
                <h3 className="text-xl justify font-semibold mb-4">Search Results</h3>

                {/* Category Tabs */}
                <div className="flex space-x-2 mb-6 overflow-x-auto pb-2">
                  {Object.entries(groupResultsByCategory(searchResults)).map(([category, items]) => 
                    items.length > 0 && (
                      <button
                        key={category}
                        onClick={() => setActiveSearchTab(category as SearchCategoryType)}
                        className={`px-4 py-2 rounded-full font-medium transition-colors whitespace-nowrap ${
                          activeSearchTab === category
                            ? 'bg-purple-400 text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        {categoryDisplayNames[category] || category}
                        <span className="ml-2 bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-sm">
                          {items.length}
                        </span>
                      </button>
                    )
                  )}
                </div>
                
                {/* Search Filters */}
                <div className="flex justify-end space-x-2 mb-6 overflow-x-auto pb-2"><SearchFilters
                  activeFilters={filters}
                  onFilterChange={setFilters}
                /></div>
                
                

                {/* Results Grid */}
                {Object.entries(groupResultsByCategory(applyFiltersAndSort(searchResults))).map(([category, items]) => 
                  items.length > 0 && category === activeSearchTab && (
                    <div key={category} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {items.map((item) => (
                        <ProductCard
                          key={item.id}
                          item={item}
                          onViewDetails={() => handleViewDetails(item)}
                        />
                      ))}
                    </div>
                  )
                )}
              </div>
            ) : searchLoading ? (
              <div className="h-full flex items-center justify-center text-gray-500">
                Searching...
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                {searchQuery ? 'No results found' : 'Type something to search'}
              </div>
            )}
          </div>
        </div>

      </div>

      {/* AI Chat Interface - Positioned absolutely */}
      <div
        className={`fixed top-[140px] right-0 bottom-[50px] w-[400px] bg-white border-l border-gray-200 transition-all duration-300 ease-in-out transform z-40 shadow-lg ${
          showAIChat ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <AIChatInterface />
      </div>

      {/* User Chat Interface - Positioned absolutely */}
      <div
        className={`fixed top-[140px] right-0 bottom-[50px] w-[400px] bg-white border-l border-gray-200 transition-all duration-300 ease-in-out transform z-40 shadow-lg ${
          showUserChat ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <ChatInterface />
      </div>

      {/* Status Bar */}
      <StatusBar
        showUserChat={showUserChat}
        showAIChat={showAIChat}
        onToggleUserChat={() => {
          if (showUserChat) {
            // If user chat is open, close it
            setShowUserChat(false);
          } else {
            // If user chat is closed, open it and close AI chat
            setShowUserChat(true);
            setShowAIChat(false);
          }
        }}
        onToggleAIChat={() => {
          if (showAIChat) {
            // If AI chat is open, close it
            setShowAIChat(false);
          } else {
            // If AI chat is closed, open it and close user chat
            setShowAIChat(true);
            setShowUserChat(false);
          }
        }}
      />

      {/* Floating AI Input Bar */}
      <div className="fixed bottom-14 left-1/2 transform -translate-x-1/2 z-50">
        <AIInputBar
          config={{
            mode: 'chat',
            model: 'gpt-4',
            mcp: 'default',
            browsing: true,
            tools: true
          }}
          onConfigChange={(newConfig) => console.log('Config changed:', newConfig)}
        />
      </div>



      {/* Item Details Modal */}
      <ItemDetailsModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        item={selectedItem}
        loading={loading}
      />
    </div>
  );
}

export default App;
