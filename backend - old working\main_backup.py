from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from motor.motor_asyncio import Async<PERSON>MotorClient
from elasticsearch import AsyncElasticsearch
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel
from bson import ObjectId
import logging
import asyncio
from utils.connection import ConnectionManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize connection manager
conn_manager = ConnectionManager(
    mongo_url="mongodb://localhost:27017/",
    es_url="http://localhost:9200"
)

# Initialize clients as None
mongo_client: Optional[AsyncIOMotorClient] = None
es_client: Optional[AsyncElasticsearch] = None
db = None

@app.on_event("startup")
async def startup_event():
    global mongo_client, es_client, db
    try:
        mongo_client, es_client = await conn_manager.connect()
        db = mongo_client["hyperlocal_db"]
        logger.info("Successfully initialized database connections")
    except Exception as e:
        logger.error(f"Failed to initialize database connections: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    await conn_manager.close()

class SearchResponse(BaseModel):
    total: int
    items: List[Dict[Any, Any]]

@app.get("/search", response_model=SearchResponse)
async def search(
    query: str = Query(..., description="Search query"),
    from_: int = Query(0, alias="from", description="Starting offset"),
    size: int = Query(10, description="Number of results per page")
) -> Dict[str, Any]:
    try:
        # Build Elasticsearch query for full-text search
        search_query = {
            "multi_match": {
                "query": query,
                "fields": ["name^2", "description", "tags", "category"],
                "type": "best_fields",
                "tie_breaker": 0.3
            }
            
        }
        logger.info(f"Elasticsearch Query: {search_query}")

        # Execute search
        response = await es_client.search(
            index="search",
            query=search_query,
            from_=from_,
            size=size
        )

        # Get MongoDB documents for the search results
        hits = response["hits"]["hits"]
        total = response["hits"]["total"]["value"]
        logger.info(f"Found {total} hits from Elasticsearch")


        # Fetch complete documents from MongoDB
        items = []
        for hit in hits:
            source = hit["_source"]
            doc_id = hit["_id"]
            collection_name = "stores" if source.get("type") == "store" else \
                            "products" if source.get("type") == "product" else "services"
            
            try:
                # Convert string ID to ObjectId for MongoDB query
                mongo_id = ObjectId(doc_id)
                mongo_doc = await db[collection_name].find_one({"_id": mongo_id})
                if mongo_doc:
                    # Convert ObjectId to string for JSON serialization
                    mongo_doc["_id"] = str(mongo_doc["_id"])
                    # Convert any nested ObjectIds to strings
                    if "store_id" in mongo_doc and isinstance(mongo_doc["store_id"], ObjectId):
                        mongo_doc["store_id"] = str(mongo_doc["store_id"])
                    items.append(mongo_doc)
                else:
                    logger.warning(f"Document not found in MongoDB: {doc_id} in {collection_name}")
            except Exception as e:
                logger.error(f"Error processing MongoDB document {doc_id}: {str(e)}")
                continue  # Skip this document but continue processing others

        return {"total": total, "items": items}

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search operation failed: {str(e)}")

@app.get("/")
async def health_check():
    services_status = {"mongodb": False, "elasticsearch": False}
    overall_status = "error"
    error_details = []

    try:
        # Check MongoDB connection
        await mongo_client.admin.command('ping')
        services_status["mongodb"] = True
    except Exception as e:
        error_msg = f"MongoDB health check failed: {str(e)}"
        logger.error(error_msg)
        error_details.append(error_msg)

    try:
        # Check Elasticsearch connection
        await es_client.ping()
        services_status["elasticsearch"] = True
    except Exception as e:
        error_msg = f"Elasticsearch health check failed: {str(e)}"
        logger.error(error_msg)
        error_details.append(error_msg)

    if all(services_status.values()):
        overall_status = "ok"
        return {
            "status": overall_status,
            "message": "All services are healthy",
            "services": services_status
        }
    else:
        raise HTTPException(
            status_code=503,
            detail={
                "status": overall_status,
                "message": "One or more services are unhealthy",
                "services": services_status,
                "errors": error_details
            }
        )

# Set SelectorEventLoop for Windows
if __name__ == "__main__":
    import platform
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)