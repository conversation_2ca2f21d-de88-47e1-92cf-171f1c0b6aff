import React, { useState } from 'react';
import { Message<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Lightbulb } from 'lucide-react';

interface StatusBarProps {
  className?: string;
  onToggleUserChat: () => void;
  onToggleAIChat: () => void;
}

const StatusBar: React.FC<StatusBarProps> = ({ className = '', onToggleUserChat, onToggleAIChat }) => {
  const [treeCount] = useState(42); // Example static count, replace with actual counter logic

  const handleFeatureRequest = () => {
    // Open feature request dialog or form
    window.open('https://github.com/yourusername/SuperApp-v1/issues/new?template=feature_request.md', '_blank');
  };

  return (
    <div className={`status-bar flex items-center justify-between px-4 py-2 bg-gray-100 ${className}`}>
      <div className="left-section flex items-center space-x-3">
        <button className="p-1 text-gray-500 rounded-full hover:bg-gray-200">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
          </svg>
        </button>
        <button className="p-1 text-gray-500 rounded-full hover:bg-gray-200">
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="1" />
            <circle cx="19" cy="12" r="1" />
            <circle cx="5" cy="12" r="1" />
          </svg>
        </button>
      </div>
      
      <div className="center-section flex space-x-3">
        <div className="trees flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M12 2L3 19h18L12 2z" />
            <path d="M12 6l-4.5 8.5h9L12 6z" />
            <path d="M12 22v-3" />
          </svg>
          <span className="text-xs font-medium text-green-600">{treeCount} trees saved</span>
        </div>
        <div className="weather flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="5" />
            <line x1="12" y1="1" x2="12" y2="3" />
            <line x1="12" y1="21" x2="12" y2="23" />
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64" />
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78" />
            <line x1="1" y1="12" x2="3" y2="12" />
            <line x1="21" y1="12" x2="23" y2="12" />
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36" />
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22" />
          </svg>
          <span className="text-xs font-medium">72°F, Sunny</span>
        </div>
        
        <div className="time flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
            <polyline points="12 6 12 12 16 14" />
          </svg>
          <span className="text-xs font-medium">3:34:59 PM</span>
        </div>
        
        <div className="status flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
          <span className="text-xs font-medium">Good</span>
        </div>
        
        <div className="cpu flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="4" y="4" width="16" height="16" rx="2" ry="2" />
            <rect x="9" y="9" width="6" height="6" />
            <line x1="9" y1="1" x2="9" y2="4" />
            <line x1="15" y1="1" x2="15" y2="4" />
            <line x1="9" y1="20" x2="9" y2="23" />
            <line x1="15" y1="20" x2="15" y2="23" />
            <line x1="20" y1="9" x2="23" y2="9" />
            <line x1="20" y1="14" x2="23" y2="14" />
            <line x1="1" y1="9" x2="4" y2="9" />
            <line x1="1" y1="14" x2="4" y2="14" />
          </svg>
          <span className="text-xs font-medium">32% CPU</span>
        </div>
        
        <div className="gpu flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M20 7h-7m-7 10h7m-9-7h4m13-3v18M6 22V6c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v2M2 18v-3a2 2 0 0 1 2-2h2" />
          </svg>
          <span className="text-xs font-medium">62% GPU</span>
        </div>
        
        <div className="connection flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M5 12.55a11 11 0 0 1 14.08 0" />
            <path d="M1.42 9a16 16 0 0 1 21.16 0" />
            <path d="M8.53 16.11a6 6 0 0 1 6.95 0" />
            <line x1="12" y1="20" x2="12.01" y2="20" />
          </svg>
          <span className="text-xs font-medium">Connected</span>
        </div>
        
        <div className="battery flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="1" y="6" width="18" height="12" rx="2" ry="2" />
            <line x1="23" y1="13" x2="23" y2="11" />
          </svg>
          <span className="text-xs font-medium">85%</span>
        </div>
        
        <div className="trees flex items-center px-2 py-1 rounded-md bg-white shadow-sm">
          <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M17 22v-4h-2l2.3-5.7a1 1 0 0 0-1.36-1.24L13 13.85V7l2.96-2.41A1 1 0 0 0 14.7 3L12 5v1.5L7.5 4.09A1 1 0 0 0 6.16 5.3L9 9v4.85l-2.94-2.79a1 1 0 0 0-1.36 1.24L7 18h-2v4" />
          </svg>
          <span className="text-xs font-medium">500B Trees Planted</span>
        </div>
      </div>
      
      <div className="right-section">
        <button className="p-1 text-green-600 font-medium rounded-md hover:bg-gray-200">
          <span className="text-xs">Trees</span>
        </button>
      </div>
      
      <div className="flex items-center space-x-4">
        <button
          onClick={onToggleUserChat}
          className="p-1.5 hover:bg-gray-700 rounded-md flex items-center space-x-1"
        >
          <MessageSquare size={16} />
          <span className="text-sm">Chat</span>
        </button>
        <button
          onClick={onToggleAIChat}
          className="p-1.5 hover:bg-gray-700 rounded-md flex items-center space-x-1"
        >
          <Bot size={16} />
          <span className="text-sm">AI Chat</span>
        </button>
        <div className="h-4 w-px bg-gray-600" /> {/* Separator */}
        <button
          onClick={handleFeatureRequest}
          className="p-1.5 hover:bg-gray-700 rounded-md flex items-center space-x-1 text-yellow-400"
        >
          <Lightbulb size={16} />
          <span className="text-sm">Request Feature</span>
        </button>
      </div>
    </div>
  );
};

export default StatusBar;
