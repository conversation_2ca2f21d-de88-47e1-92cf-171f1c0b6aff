from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from elasticsearch import AsyncElasticsearch
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import sys

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Elasticsearch connection (using async client)
es_client = AsyncElasticsearch(["http://localhost:9200"])

class SearchResponse(BaseModel):
    total: int
    items: List[Dict[Any, Any]]

async def run_query(query_body: dict, index_name: str = "search") -> dict:
    """
    Executes an Elasticsearch query and returns the results.

    Args:
        query_body: The Elasticsearch query in dictionary format.
        index_name: The name of the index to query.

    Returns:
        The raw Elasticsearch response as a dictionary.

    Raises:
        HTTPException: If there's an error executing the query.
    """
    try:
        response = await es_client.search(index=index_name, query=query_body)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Elasticsearch query error: {e}")

@app.get("/api/all", response_model=SearchResponse)
async def get_all_documents() -> dict:
    """
    Retrieves all documents from the 'search' index.
    """
    query = {"match_all": {}}
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/name", response_model=SearchResponse)
async def search_by_name(
    query_string: str = Query(..., description="Text to search in the name field")
) -> dict:
    """
    Performs a text search on the "name" field using the my_analyzer.
    """
    query = {
        "match": {
            "name": {
                "query": query_string,
                "analyzer": "my_analyzer"
            }
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/name_keyword", response_model=SearchResponse)
async def search_by_name_keyword(
    query_string: str = Query(..., description="Exact string to match in the name.keyword field")
) -> dict:
    """
    Performs an exact match search on the "name.keyword" field.
    """
    query = {
        "term": {
            "name.keyword": query_string
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/category", response_model=SearchResponse)
async def filter_by_category(
    category_name: str = Query(..., description="Category name to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "category.keyword" field for an exact match.
    """
    query = {
        "term": {
            "category.keyword": category_name
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/category_hierarchy", response_model=SearchResponse)
async def filter_by_category_hierarchy(
    category_hierarchy: str = Query(..., description="Category hierarchy to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "category.hierarchy" field for an exact match.
    """
    query = {
        "term": {
            "category.hierarchy": category_hierarchy
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/description", response_model=SearchResponse)
async def search_description(
    query_string: str = Query(..., description="Text to search in the description field")
) -> dict:
    """
    Performs a text search on the "description" field using the english analyzer.
    """
    query = {
        "match": {
            "description": query_string
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/color", response_model=SearchResponse)
async def filter_by_color(
    color_name: str = Query(..., description="Color name to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "attributes.color.keyword" field.
    """
    query = {
        "term": {
            "attributes.color.keyword": color_name
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/type", response_model=SearchResponse)
async def filter_by_type(
    item_type: str = Query(..., description="Item type to filter by (e.g., product, store)")
) -> dict:
    """
    Filters documents by the "type" field.
    """
    query = {
        "term":{
            "type": item_type
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search", response_model=SearchResponse)
async def search_with_multiple_criteria(
    query: str = Query(..., description="Text to search in name and description"),
    category: Optional[str] = Query(None, description="Category name to filter by"),
    item_type: Optional[str] = Query(None, description="Item type to filter by (e.g., product, store)")
) -> dict:
    """
    Combines a text search with category and type filters.
    """
    search_query = {
        "bool": {
            "must": [
                {
                    "multi_match": {
                        "query": query,
                        "fields": ["name", "description"]
                    }
                }
            ],
            "filter": []
        }
    }
    if category:
        search_query["bool"]["filter"].append({"term": {"category.keyword": category}})
    if item_type:
        search_query["bool"]["filter"].append({"term": {"type": item_type}})
    response = await run_query(search_query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

if __name__ == "__main__":
    import uvicorn
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    uvicorn.run(app, host="0.0.0.0", port=8000)

