from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from elasticsearch import AsyncElasticsearch
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
import asyncio
import sys
from fastapi.middleware.cors import CORSMiddleware



app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Elasticsearch connection (using async client)
es_client = AsyncElasticsearch(["http://localhost:9200"])

class LocationCoordinates(BaseModel):
    lat: float = Field(..., description="Latitude coordinate")
    lon: float = Field(..., description="Longitude coordinate")
    radius: float = Field(5.0, description="Search radius in kilometers")

class SearchWithLocationRequest(BaseModel):
    query: str = Field(..., description="Text to search in name and description")
    location: LocationCoordinates = Field(..., description="Location coordinates for search context")
    from_: int = Field(0, alias="from", description="Starting offset for results")
    size: int = Field(20, description="Number of results to return")

class LocationSearchRequest(BaseModel):
    lat: float = Field(..., description="Latitude coordinate")
    lon: float = Field(..., description="Longitude coordinate")
    radius: float = Field(5.0, description="Search radius in kilometers")
    from_: int = Field(0, alias="from", description="Starting offset for results")
    size: int = Field(20, description="Number of results to return")

class SearchResponse(BaseModel):
    total: int
    items: List[Dict[Any, Any]]

async def run_query(query_body: dict, index_name: str = "search") -> dict:
    """
    Executes an Elasticsearch query and returns the results.

    Args:
        query_body: The Elasticsearch query in dictionary format.
        index_name: The name of the index to query.

    Returns:
        The raw Elasticsearch response as a dictionary.

    Raises:
        HTTPException: If there's an error executing the query.
    """
    try:
        response = await es_client.search(index=index_name, query=query_body) # Corrected line
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Elasticsearch query error: {e}")

@app.get("/api/all", response_model=SearchResponse)
async def get_all_documents() -> dict:
    """
    Retrieves all documents from the 'search' index.
    """
    query = {"match_all": {}}
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/name", response_model=SearchResponse)
async def search_by_name(
    query_string: str = Query(..., description="Text to search in the name field")
) -> dict:
    """
    Performs a text search on the "name" field using the my_analyzer.
    """
    query = {
        "match": {
            "name": {
                "query": query_string,
                "analyzer": "my_analyzer"
            }
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/name_keyword", response_model=SearchResponse)
async def search_by_name_keyword(
    query_string: str = Query(..., description="Exact string to match in the name.keyword field")
) -> dict:
    """
    Performs an exact match search on the "name.keyword" field.
    """
    query = {
        "term": {
            "name.keyword": query_string
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/category", response_model=SearchResponse)
async def filter_by_category(
    category_name: str = Query(..., description="Category name to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "category.keyword" field for an exact match.
    """
    query = {
        "term": {
            "category.keyword": category_name
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/category_hierarchy", response_model=SearchResponse)
async def filter_by_category_hierarchy(
    category_hierarchy: str = Query(..., description="Category hierarchy to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "category.hierarchy" field for an exact match.
    """
    query = {
        "term": {
            "category.hierarchy": category_hierarchy
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search/description", response_model=SearchResponse)
async def search_description(
    query_string: str = Query(..., description="Text to search in the description field")
) -> dict:
    """
    Performs a text search on the "description" field using the english analyzer.
    """
    query = {
        "match": {
            "description": query_string
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/color", response_model=SearchResponse)
async def filter_by_color(
    color_name: str = Query(..., description="Color name to filter by (exact match)")
) -> dict:
    """
    Filters documents by the "attributes.color.keyword" field.
    """
    query = {
        "term": {
            "attributes.color.keyword": color_name
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/filter/type", response_model=SearchResponse)
async def filter_by_type(
    item_type: str = Query(..., description="Item type to filter by (e.g., product, store)")
) -> dict:
    """
    Filters documents by the "type" field.
    """
    query = {
        "term":{
            "type": item_type
        }
    }
    response = await run_query(query)
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

@app.get("/api/search", response_model=SearchResponse)
async def search_with_multiple_criteria(
    query: str = Query(..., description="Text to search in name and description"),
    category: Optional[str] = Query(None, description="Category name to filter by"),
    location: Optional[str] = Query(None, description="Location to filter by"), # Added location
    item_type: Optional[str] = Query(None, description="Item type to filter by (e.g., product, store)")
) -> dict:
    """
    Combines a text search with category, location, and type filters.
    """
    search_query = {
        "bool": {
            "must": [
                {
                    "multi_match": {
                        "query": query,
                        "fields": ["name", "description"]
                    }
                }
            ],
            "filter": []
        }
    }
    if category:
        search_query["bool"]["filter"].append({
            "term": {  # Changed back to term
                "category.keyword": category # specifying the sub-field
            }
        })
    if location:
        search_query["bool"]["filter"].append({"match": {"location.address": location}}) # Added location filter
    if item_type:
        search_query["bool"]["filter"].append({"term": {"type": item_type}})
    response = await run_query(search_query, index_name="search") # Added index_name
    return {
        "total": response["hits"]["total"]["value"],
        "items": [hit["_source"] for hit in response["hits"]["hits"]]
    }

# Add these endpoints to your FastAPI application

@app.get("/api/products/{id}")
async def get_product_by_id(id: str):
    """
    Retrieves a product by ID from MongoDB.
    """
    try:
        # Convert string ID to ObjectId
        from bson.objectid import ObjectId
        mongo_id = ObjectId(id)
        
        # Fetch from MongoDB
        product = await db["products"].find_one({"_id": mongo_id})
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")
        
        # Convert ObjectId to string for JSON serialization
        product["_id"] = str(product["_id"])
        if "store_id" in product and isinstance(product["store_id"], ObjectId):
            product["store_id"] = str(product["store_id"])
            
        return product
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get("/api/services/{id}")
async def get_service_by_id(id: str):
    """
    Retrieves a service by ID from MongoDB.
    """
    # Similar implementation as get_product_by_id but for services collection
    pass

@app.get("/api/stores/{id}")
async def get_store_by_id(id: str):
    """
    Retrieves a store by ID from MongoDB.
    """
    # Similar implementation as get_product_by_id but for stores collection
    pass

@app.get("/api/search/{id}")
async def get_item_by_id(id: str):
    """
    Retrieves an item by ID from MongoDB.
    Checks all collections (products, services, stores) and returns the first match.
    """
    try:
        # Convert string ID to ObjectId
        from bson.objectid import ObjectId
        try:
            mongo_id = ObjectId(id)
        except:
            # Handle case where ID is not a valid ObjectId
            raise HTTPException(status_code=400, detail="Invalid ID format")
        
        # Try each collection
        collections = ["products", "services", "stores"]
        for collection_name in collections:
            item = await db[collection_name].find_one({"_id": mongo_id})
            if item:
                # Convert ObjectId to string for JSON serialization
                item["_id"] = str(item["_id"])
                # Handle nested ObjectIds
                if "store_id" in item and isinstance(item["store_id"], ObjectId):
                    item["store_id"] = str(item["store_id"])
                return item
        
        # If no item found in any collection
        raise HTTPException(status_code=404, detail="Item not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    
@app.post("/api/search/location", response_model=SearchResponse)
async def search_by_location(request: LocationSearchRequest):
    """
    Searches for items near a specific location.
    """
    # Create a geo distance query
    query = {
        "bool": {
            "filter": [
                {
                    "geo_distance": {
                        "distance": f"{request.radius}km",
                        "location.coordinates": {
                            "lat": request.lat,
                            "lon": request.lon
                        }
                    }
                }
            ]
        }
    }
    
    try:
        # Execute the query with pagination
        response = await es_client.search(
            index="search",
            body={
                "query": query,
                "from": request.from_,
                "size": request.size,
                "sort": [
                    {
                        "_geo_distance": {
                            "location.coordinates": {
                                "lat": request.lat,
                                "lon": request.lon
                            },
                            "order": "asc",
                            "unit": "km"
                        }
                    }
                ]
            }
        )
        
        # Process results and add distance information
        items = []
        for hit in response["hits"]["hits"]:
            item = hit["_source"]
            if "_geo_distance" in hit:
                item["distance"] = {
                    "value": hit["_geo_distance"],
                    "unit": "km"
                }
            items.append(item)
            
        return {
            "total": response["hits"]["total"]["value"],
            "items": items
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Location search error: {str(e)}")