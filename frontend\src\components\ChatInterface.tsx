import React from 'react';

const ChatInterface: React.FC = () => {
  return (
    <div className="h-full flex flex-col bg-white">
      {/* Chat Messages Area */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Example messages - replace with actual chat messages */}
        <div className="mb-4">
          <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
            <p className="text-sm">Hello! How can I help you today?</p>
          </div>
        </div>
      </div>

      {/* Chat Input Area */}
      <div className="border-t border-gray-200 p-3">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-purple-400"
          />
          <button className="px-4 py-2 bg-purple-400 text-white rounded-lg hover:bg-purple-700">
            Send
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
