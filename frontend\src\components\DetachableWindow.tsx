import React, { useState } from 'react';
import { Minus, X, Maximize2, Minimize2 } from 'lucide-react';

interface DetachableWindowProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  defaultPosition?: { x: number; y: number };
  defaultSize?: { width: number; height: number };
  children: React.ReactNode;
}

const DetachableWindow: React.FC<DetachableWindowProps> = ({
  title,
  isOpen,
  onClose,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 384, height: 500 },
  children
}) => {
  const [position, setPosition] = useState(defaultPosition);
  const [size, setSize] = useState(defaultSize);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isMinimized, setIsMinimized] = useState(false);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target instanceof HTMLElement && e.target.closest('.window-controls')) {
      return;
    }
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  React.useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden flex flex-col"
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: isMinimized ? '40px' : size.height,
        transition: 'height 0.2s ease-in-out',
        zIndex: 45
      }}
    >
      {/* Window Title Bar */}
      <div
        className="bg-purple-400 text-white px-4 py-2 cursor-move flex items-center justify-between select-none"
        onMouseDown={handleMouseDown}
      >
        <span className="font-medium">{title}</span>
        <div className="window-controls flex items-center space-x-2">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-purple-400 rounded"
          >
            {isMinimized ? <Maximize2 size={14} /> : <Minus size={14} />}
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-purple-400 rounded"
          >
            <X size={14} />
          </button>
        </div>
      </div>

      {/* Window Content */}
      <div className={`flex-1 overflow-hidden ${isMinimized ? 'hidden' : ''}`}>
        {children}
      </div>

      {/* Resize Handle */}
      {!isMinimized && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
          onMouseDown={(e) => {
            e.stopPropagation();
            const startSize = size;
            const startPos = { x: e.clientX, y: e.clientY };

            const handleResize = (e: MouseEvent) => {
              setSize({
                width: startSize.width + (e.clientX - startPos.x),
                height: startSize.height + (e.clientY - startPos.y)
              });
            };

            const handleMouseUp = () => {
              window.removeEventListener('mousemove', handleResize);
              window.removeEventListener('mouseup', handleMouseUp);
            };

            window.addEventListener('mousemove', handleResize);
            window.addEventListener('mouseup', handleMouseUp);
          }}
        >
          <div className="w-0 h-0 border-l-[8px] border-l-transparent border-t-[8px] border-t-gray-300" />
        </div>
      )}
    </div>
  );
};

export default DetachableWindow; 