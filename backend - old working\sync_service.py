from typing import Dict, Any, Optional
from motor.motor_asyncio import AsyncIOMotorClient
from elasticsearch import AsyncElasticsearch
from datetime import datetime
import logging
import asyncio
from pymongo.errors import OperationFailure

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SyncService:
    def __init__(self, mongo_client: AsyncIOMotorClient, es_client: AsyncElasticsearch):
        self.mongo_client = mongo_client
        self.es_client = es_client
        self.db = mongo_client["hyperlocal_db"]
        self.collections = ["stores", "products", "services"]
        self.last_sync_timestamps = {}
        self.polling_interval = 5  # seconds
        self.max_retries = 3
        self.retry_delay = 1  # seconds
        self._running = False
        
        # Ensure indexes for optimized polling
        asyncio.create_task(self._ensure_indexes())

    def _get_document_type(self, collection_name: str) -> str:
        return collection_name[:-1]  # Remove 's' from plural form

    def _prepare_document_for_es(self, doc: Dict[Any, Any], collection_name: str) -> Dict[Any, Any]:
        # Create a copy and remove _id since it will be passed separately
        doc_copy = doc.copy()
        doc_copy.pop("_id", None)  # Remove _id if present
        
        # Convert ObjectId to string for store_id field
        if "store_id" in doc_copy and hasattr(doc_copy["store_id"], "__str__"):
            doc_copy["store_id"] = str(doc_copy["store_id"])
            
        doc_copy["type"] = self._get_document_type(collection_name)
        doc_copy["updated_at"] = datetime.utcnow().isoformat()
        return doc_copy

    async def sync_document(self, doc: Dict[Any, Any], collection_name: str, operation: str) -> None:
        doc_id = str(doc["_id"])
        
        for attempt in range(self.max_retries):
            try:
                if operation in ["insert", "update", "replace"]:
                    es_doc = self._prepare_document_for_es(doc, collection_name)
                    await self.es_client.index(
                        index="search",
                        id=doc_id,
                        document=es_doc,
                        refresh=True
                    )
                    logger.info(f"Indexed document {doc_id} from {collection_name}")
                    return
                
                elif operation == "delete":
                    await self.es_client.delete(
                        index="search",
                        id=doc_id,
                        refresh=True
                    )
                    logger.info(f"Deleted document {doc_id} from search index")
                    return

            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed syncing document {doc_id}: {str(e)}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(self.retry_delay)

    async def watch_collections(self) -> None:
        try:
            # Try to use change streams first
            await self._watch_with_change_streams()
        except OperationFailure as e:
            if "The $changeStream stage is only supported on replica sets" in str(e):
                logger.info("Falling back to polling-based sync for standalone MongoDB")
                await self._watch_with_polling()
            else:
                raise

    async def _watch_with_change_streams(self) -> None:
        for collection_name in self.collections:
            collection = self.db[collection_name]
            change_stream = collection.watch()
            logger.info(f"Watching collection with change streams: {collection_name}")

            try:
                async with change_stream as stream:
                    async for change in stream:
                        operation = change["operationType"]
                        
                        if operation == "insert":
                            doc = change["fullDocument"]
                            await self.sync_document(doc, collection_name, "insert")
                        
                        elif operation in ["update", "replace"]:
                            doc_id = change["documentKey"]["_id"]
                            doc = await collection.find_one({"_id": doc_id})
                            if doc:
                                await self.sync_document(doc, collection_name, operation)
                        
                        elif operation == "delete":
                            doc_id = change["documentKey"]["_id"]
                            await self.sync_document({"_id": doc_id}, collection_name, "delete")

            except Exception as e:
                logger.error(f"Error in change stream for {collection_name}: {str(e)}")
                raise

    async def _watch_with_polling(self) -> None:
        self._running = True
        while self._running:
            try:
                for collection_name in self.collections:
                    await self._poll_collection(collection_name)
                await asyncio.sleep(self.polling_interval)
            except Exception as e:
                logger.error(f"Error in polling sync: {str(e)}")
                await asyncio.sleep(1)  # Brief pause before retrying

    async def _poll_collection(self, collection_name: str) -> None:
        collection = self.db[collection_name]
        last_sync = self.last_sync_timestamps.get(collection_name)
        
        query = {}
        if last_sync:
            query["updated_at"] = {"$gt": last_sync}
        
        async for doc in collection.find(query):
            await self.sync_document(doc, collection_name, "update")
        
        # Update last sync timestamp
        self.last_sync_timestamps[collection_name] = datetime.utcnow()

    async def stop(self) -> None:
        """Stop the polling sync process"""
        self._running = False

    async def initial_sync(self) -> None:
        """Perform initial sync of existing documents"""
        try:
            for collection_name in self.collections:
                collection = self.db[collection_name]
                cursor = collection.find({})
                
                async for doc in cursor:
                    await self.sync_document(doc, collection_name, "insert")
                
                # Initialize last sync timestamp after successful sync
                self.last_sync_timestamps[collection_name] = datetime.utcnow()
                logger.info(f"Completed initial sync for {collection_name}")

        except Exception as e:
            logger.error(f"Error during initial sync: {str(e)}")
            raise

    async def _ensure_indexes(self) -> None:
        """Ensure required indexes exist on collections"""
        try:
            for collection_name in self.collections:
                collection = self.db[collection_name]
                await collection.create_index("updated_at", background=True)
                logger.info(f"Created index on updated_at for {collection_name}")
        except Exception as e:
            logger.error(f"Error creating indexes: {str(e)}")

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            await self.stop()
            await self.mongo_client.close()
            await self.es_client.close()
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")