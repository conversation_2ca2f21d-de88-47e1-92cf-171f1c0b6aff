from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from elasticsearch import Async<PERSON>lasticsearch
import asyncio
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class ConnectionManager:
    def __init__(self, mongo_url: str, es_url: str):
        self.mongo_url = mongo_url
        self.es_url = es_url
        self.mongo_client: Optional[AsyncIOMotorClient] = None
        self.es_client: Optional[AsyncElasticsearch] = None
        self.max_retries = 3
        self.retry_delay = 1  # seconds

    async def connect(self) -> Tuple[AsyncIOMotorClient, AsyncElasticsearch]:
        """Initialize connections to MongoDB and Elasticsearch with retry logic"""
        await self._connect_mongo()
        await self._connect_elasticsearch()
        return self.mongo_client, self.es_client

    async def _connect_mongo(self) -> None:
        for attempt in range(self.max_retries):
            try:
                if not self.mongo_client:
                    self.mongo_client = AsyncIOMotorClient(
                        self.mongo_url,
                        serverSelectionTimeoutMS=5000
                    )
                await self.mongo_client.admin.command('ping')
                logger.info("Successfully connected to MongoDB")
                return
            except Exception as e:
                logger.error(f"MongoDB connection attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(self.retry_delay)

    async def _connect_elasticsearch(self) -> None:
        for attempt in range(self.max_retries):
            try:
                if not self.es_client:
                    self.es_client = AsyncElasticsearch([self.es_url])
                await self.es_client.ping()
                logger.info("Successfully connected to Elasticsearch")
                return
            except Exception as e:
                logger.error(f"Elasticsearch connection attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.max_retries - 1:
                    raise
                await asyncio.sleep(self.retry_delay)

    async def close(self) -> None:
        """Close all connections"""
        try:
            if self.mongo_client:
                await self.mongo_client.close()
            if self.es_client:
                await self.es_client.close()
        except Exception as e:
            logger.error(f"Error closing connections: {str(e)}")